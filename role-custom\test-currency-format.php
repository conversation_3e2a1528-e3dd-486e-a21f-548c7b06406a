<?php
/**
 * Para Birimi Format Test
 * Bu dosya para birimi formatının doğru gösterildiğini test eder
 */

// WordPress yüklenmesini bekle
if (!defined('ABSPATH')) {
    exit('WordPress yüklenmedi!');
}

/**
 * Para birimi format testini çalıştır
 */
function test_currency_format() {
    echo "<h2>Para Birimi Format Test</h2>";
    
    // Sadece admin kullanıcılar için
    if (!current_user_can('manage_options')) {
        echo "<p style='color: red;'>Bu sayfaya erişim yetkiniz yok.</p>";
        return;
    }
    
    // Role Custom instance'ını al
    if (!class_exists('Role_Custom')) {
        echo "<p style='color: red;'>❌ Role_Custom sınıfı bulunamadı</p>";
        return;
    }
    
    $role_custom = Role_Custom::get_instance();
    $reflection = new ReflectionClass($role_custom);
    $method = $reflection->getMethod('get_user_stats');
    $method->setAccessible(true);
    $stats = $method->invoke($role_custom);
    
    echo "<h3>Mevcut İstatistikler</h3>";
    echo "<table border='1' cellpadding='5' cellspacing='0'>";
    echo "<tr><th>Alan</th><th>Ham Değer</th><th>Tip</th><th>Formatlanmış</th></tr>";
    
    foreach ($stats as $key => $value) {
        $type = gettype($value);
        $formatted = '';
        
        if ($key === 'total_revenue') {
            $formatted = get_woocommerce_currency_symbol() . ' ' . number_format($value, 2, ',', '.');
        } elseif (is_numeric($value)) {
            $formatted = number_format($value, 0, ',', '.');
        } else {
            $formatted = $value;
        }
        
        echo "<tr>";
        echo "<td><strong>{$key}</strong></td>";
        echo "<td>{$value}</td>";
        echo "<td>{$type}</td>";
        echo "<td>{$formatted}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h3>WooCommerce Para Birimi Bilgileri</h3>";
    echo "<table border='1' cellpadding='5' cellspacing='0'>";
    echo "<tr><th>Özellik</th><th>Değer</th></tr>";
    
    if (function_exists('get_woocommerce_currency')) {
        echo "<tr><td>Para Birimi Kodu</td><td>" . get_woocommerce_currency() . "</td></tr>";
    }
    
    if (function_exists('get_woocommerce_currency_symbol')) {
        $symbol = get_woocommerce_currency_symbol();
        echo "<tr><td>Para Birimi Sembolü</td><td>{$symbol}</td></tr>";
        echo "<tr><td>HTML Entity</td><td>" . htmlentities($symbol) . "</td></tr>";
        echo "<tr><td>Raw HTML</td><td>" . esc_html($symbol) . "</td></tr>";
    }
    
    if (function_exists('wc_get_price_decimal_separator')) {
        echo "<tr><td>Ondalık Ayırıcı</td><td>" . wc_get_price_decimal_separator() . "</td></tr>";
    }
    
    if (function_exists('wc_get_price_thousand_separator')) {
        echo "<tr><td>Binlik Ayırıcı</td><td>" . wc_get_price_thousand_separator() . "</td></tr>";
    }
    
    if (function_exists('wc_get_price_decimals')) {
        echo "<tr><td>Ondalık Basamak</td><td>" . wc_get_price_decimals() . "</td></tr>";
    }
    
    echo "</table>";
    
    echo "<h3>JavaScript Test</h3>";
    echo "<p>Aşağıdaki değerler JavaScript ile formatlanacak:</p>";
    
    echo "<div id='js-test-results'>";
    echo "<p>Test değeri: <span id='test-value'>" . $stats['total_revenue'] . "</span></p>";
    echo "<p>Formatlanmış: <span id='formatted-value'>-</span></p>";
    echo "</div>";
    
    echo "<script>
    document.addEventListener('DOMContentLoaded', function() {
        const testValue = " . $stats['total_revenue'] . ";
        const currencySymbol = '" . esc_js(get_woocommerce_currency_symbol()) . "';
        const formatted = currencySymbol + ' ' + testValue.toLocaleString('tr-TR', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        });

        document.getElementById('formatted-value').textContent = formatted;

        console.log('Test Value:', testValue);
        console.log('Formatted:', formatted);
    });
    </script>";
    
    echo "<h3>Önerilen Çözümler</h3>";
    echo "<ul>";
    echo "<li>✅ JavaScript'te <code>toLocaleString('tr-TR')</code> kullanılıyor</li>";
    echo "<li>✅ WooCommerce para birimi sembolü kullanılıyor</li>";
    echo "<li>✅ Format: Sembol + Boşluk + Sayı</li>";
    echo "<li>✅ Ondalık basamaklar sabitlendi (2 basamak)</li>";
    echo "</ul>";
    
    echo "<h3>Test Sonucu</h3>";
    if ($stats['total_revenue'] > 0) {
        echo "<p style='color: green;'>✅ Test verisi mevcut, formatlanmış değeri kontrol edin</p>";
    } else {
        echo "<p style='color: orange;'>⚠️ Toplam gelir 0, test için sipariş oluşturun</p>";
    }
    
    echo "<p><strong>Beklenen format:</strong> ₺ 1.234,56</p>";
    echo "<p><strong>Eski format (sorunlu):</strong> &#8378; 75</p>";
}

// Admin panelinde test çalıştır
if (is_admin() && current_user_can('manage_options')) {
    add_action('admin_notices', function() {
        if (isset($_GET['test_currency_format'])) {
            echo '<div class="notice notice-info is-dismissible" style="max-width: 1200px;">';
            test_currency_format();
            echo '</div>';
        }
    });
    
    // Test linkini admin bar'a ekle
    add_action('admin_bar_menu', function($wp_admin_bar) {
        $wp_admin_bar->add_node([
            'id' => 'test-currency-format',
            'title' => 'Test Currency Format',
            'href' => admin_url('admin.php?test_currency_format=1'),
            'meta' => ['title' => 'Test Currency Format']
        ]);
    }, 100);
}
