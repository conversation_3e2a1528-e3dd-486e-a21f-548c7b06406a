<?php
/**
 * Plugin Name: Role Custom
 * Plugin URI: https://example.com/role-custom
 * Description: WordPress eklentisi - Superole rolü oluşturur ve Tutor LMS menülerini kısıtlar, WooCommerce'e tam er<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> gizler.
 * Version: 1.0.0
 * Author: Role Custom Developer
 * Author URI: https://example.com
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain: role-custom
 * Domain Path: /languages
 * Requires at least: 5.0
 * Tested up to: 6.4
 * Requires PHP: 7.4
 * Network: false
 */

// Doğrudan erişimi engelle
if (!defined('ABSPATH')) {
    exit;
}

// Eklenti sabitleri
define('ROLE_CUSTOM_VERSION', '1.0.0');
define('ROLE_CUSTOM_PLUGIN_FILE', __FILE__);
define('ROLE_CUSTOM_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('ROLE_CUSTOM_PLUGIN_URL', plugin_dir_url(__FILE__));
define('ROLE_CUSTOM_PLUGIN_BASENAME', plugin_basename(__FILE__));

/**
 * Ana Role Custom sınıfı
 */
class Role_Custom {
    
    /**
     * Singleton instance
     */
    private static $instance = null;
    
    /**
     * Superole rol adı
     */
    const SUPEROLE_ROLE = 'superole';
    
    /**
     * Tutor LMS'de izin verilen menüler
     */
    private $allowed_tutor_menus = [
        'tutor',                           // Kurslar (ana sayfa)
        'tutor-students',                  // Öğrenciler
        'tutor_announcements',             // Duyurular
        'question_answer',                 // Q&A
        'tutor_quiz_attempts'              // Sınav Denemeleri
    ];
    
    /**
     * Singleton pattern
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        $this->init_hooks();
    }
    
    /**
     * Hook'ları başlat
     */
    private function init_hooks() {
        // Admin hook'ları
        add_action('admin_menu', [$this, 'restrict_tutor_menus'], 999);
        add_action('admin_menu', [$this, 'restrict_woocommerce_menus'], 999);
        add_action('admin_menu', [$this, 'remove_payments_menu'], 9999);
        add_action('admin_menu', [$this, 'hide_marketing_menu'], 9999);
        add_action('admin_menu', [$this, 'hide_product_taxonomies'], 9999);
        add_action('admin_menu', [$this, 'create_woocommerce_top_level_menus'], 10000);
        add_action('admin_init', [$this, 'setup_woocommerce_capabilities']);
        add_action('admin_init', [$this, 'redirect_dashboard_to_tutor']);
        add_action('admin_notices', [$this, 'admin_notices']);

        // Menü kısıtlama hook'ları
        add_action('admin_head', [$this, 'hide_restricted_menus_css']);
        add_filter('user_has_cap', [$this, 'filter_user_capabilities'], 10, 4);

        // Dil dosyalarını yükle
        add_action('plugins_loaded', [$this, 'load_textdomain']);

        // Admin init'te rol kontrolü yap
        add_action('admin_init', [$this, 'check_and_create_role']);

        // Eski kalıcı meta veri sistemini temizle
        add_action('admin_init', [$this, 'cleanup_old_superole_instructor_system'], 5);

        // Tutor LMS eğitmen kontrollerini superole için geçersiz kıl
        add_filter('user_has_cap', [$this, 'grant_tutor_instructor_cap_to_superole'], 10, 4);

        // Tutor LMS'in is_instructor() metodunu superole için geçersiz kıl
        add_filter('get_user_metadata', [$this, 'override_tutor_instructor_meta_for_superole'], 10, 4);

        // Tutor LMS dinamik eğitmen sistemi için hook'lar - Kapsamlı rol değişikliği yakalama
        add_action('set_user_role', [$this, 'handle_superole_role_change'], 10, 3);
        add_action('add_user_role', [$this, 'handle_superole_role_add'], 10, 2);
        add_action('remove_user_role', [$this, 'handle_superole_role_remove'], 10, 2);

        // WordPress admin panelindeki rol değişikliklerini yakala
        add_action('profile_update', [$this, 'handle_profile_role_update'], 10, 2);
        add_action('user_profile_update_errors', [$this, 'capture_old_user_roles'], 10, 3);

        // WP_User::set_role() metodunu yakala
        add_action('wp_login', [$this, 'check_user_role_on_login'], 10, 2);

        // WooCommerce veri filtreleme hook'ları
        add_action('pre_get_posts', [$this, 'filter_woocommerce_data_for_superole']);
        add_filter('posts_where', [$this, 'filter_orders_by_user_products'], 10, 2);
        add_filter('woocommerce_orders_table_query_clauses', [$this, 'filter_hpos_orders_by_user_products'], 10, 2);

        // WooCommerce admin sayfalarında ek filtreleme
        add_action('load-edit.php', [$this, 'add_woocommerce_admin_filters']);
        add_action('load-admin.php', [$this, 'add_woocommerce_admin_filters']);

        // WooCommerce admin siparişler sayfası için özel hook
        add_action('current_screen', [$this, 'handle_orders_screen']);

        // HPOS için ek hook'lar
        add_action('woocommerce_order_list_table_prepare_items', [$this, 'filter_hpos_order_list']);
        add_filter('woocommerce_order_query_args', [$this, 'filter_order_query_args'], 10, 1);

        // HPOS için en kritik hook - wc_get_orders fonksiyonunu filtrele
        add_filter('woocommerce_orders_table_query_clauses', [$this, 'filter_hpos_orders_by_user_products'], 10, 2);
        add_filter('wc_get_orders_args', [$this, 'filter_wc_get_orders_args'], 10, 1);

        // WooCommerce admin sayfalarında JavaScript ile filtreleme
        add_action('admin_footer', [$this, 'add_order_filtering_js']);

        // WooCommerce ürün row actions'larını düzelt
        add_filter('post_row_actions', [$this, 'fix_product_row_actions'], 999, 2);

        // Ürün düzenleme yetkilerini güçlendir
        add_filter('map_meta_cap', [$this, 'fix_product_edit_capabilities'], 10, 4);

        // Admin sayfalarında JavaScript ile row actions'ları düzelt
        add_action('admin_footer', [$this, 'add_product_row_actions_js']);

        // Kullanıcı düzenleme sayfasında rol değişikliği uyarısı
        add_action('admin_footer-user-edit.php', [$this, 'add_role_change_warning_js']);

        // Reports menü sayfası için hook'lar
        add_action('admin_menu', [$this, 'add_reports_menu'], 10001);
        add_action('wp_ajax_role_custom_get_sales_data', [$this, 'ajax_get_sales_data']);
        add_action('wp_ajax_role_custom_get_revenue_data', [$this, 'ajax_get_revenue_data']);
        add_action('wp_ajax_role_custom_get_product_performance', [$this, 'ajax_get_product_performance']);
        add_action('admin_enqueue_scripts', [$this, 'enqueue_reports_scripts']);
    }

    /**
     * Kullanıcı rolü değiştirildiğinde superole durumunu kontrol et
     */
    public function handle_superole_role_change($user_id, $role, $old_roles) {
        // Eski rollerde superole varsa ve yeni rol superole değilse, eğitmen meta verilerini temizle
        if (in_array(self::SUPEROLE_ROLE, $old_roles) && $role !== self::SUPEROLE_ROLE) {
            $this->cleanup_superole_instructor_meta($user_id);
        }
    }

    /**
     * Kullanıcıya rol eklendiğinde superole durumunu kontrol et
     */
    public function handle_superole_role_add($user_id, $role) {
        // Superole rolü eklendiğinde özel bir işlem yapmıyoruz
        // Capability filter sistemi otomatik olarak eğitmen yetkilerini verecek
    }

    /**
     * Kullanıcıdan rol kaldırıldığında superole durumunu kontrol et
     */
    public function handle_superole_role_remove($user_id, $role) {
        // Superole rolü kaldırıldığında eğitmen meta verilerini temizle
        if ($role === self::SUPEROLE_ROLE) {
            $this->cleanup_superole_instructor_meta($user_id);
            $this->clear_user_caches($user_id);
        }
    }

    // Kullanıcının eski rollerini saklamak için geçici değişken
    private $user_old_roles = [];

    /**
     * Profil güncellemesinden önce eski rolleri yakala
     */
    public function capture_old_user_roles($errors, $update, $user) {
        if ($update && isset($user->ID)) {
            $old_user = get_userdata($user->ID);
            if ($old_user) {
                $this->user_old_roles[$user->ID] = $old_user->roles;
            }
        }
    }

    /**
     * Profil güncellemesi sonrası rol değişikliklerini kontrol et
     */
    public function handle_profile_role_update($user_id, $old_user_data) {
        $new_user = get_userdata($user_id);
        $old_roles = isset($this->user_old_roles[$user_id]) ? $this->user_old_roles[$user_id] : $old_user_data->roles;
        $new_roles = $new_user->roles;

        // Superole rolü değişikliklerini kontrol et
        $had_superole = in_array(self::SUPEROLE_ROLE, $old_roles);
        $has_superole = in_array(self::SUPEROLE_ROLE, $new_roles);

        if ($had_superole && !$has_superole) {
            // Superole rolü kaldırıldı
            $this->cleanup_superole_instructor_meta($user_id);
            $this->clear_user_caches($user_id);
            error_log("Role Custom: Kullanıcı (ID: {$user_id}) superole rolünü kaybetti, eğitmen meta verileri temizlendi.");
        }

        // Geçici veriyi temizle
        unset($this->user_old_roles[$user_id]);
    }

    /**
     * Kullanıcı giriş yaptığında rol durumunu kontrol et
     */
    public function check_user_role_on_login($user_login, $user) {
        // Sadece superole kullanıcıları için kontrol et
        if (!in_array(self::SUPEROLE_ROLE, $user->roles)) {
            return;
        }

        // Eğer superole kullanıcısının eğitmen meta verileri varsa temizle
        // (Çünkü artık dinamik sistem kullanıyoruz)
        $has_meta = get_user_meta($user->ID, '_is_tutor_instructor', true);
        if ($has_meta) {
            // Gerçek eğitmen rolü yoksa meta verileri temizle
            if (!in_array('tutor_instructor', $user->roles)) {
                delete_user_meta($user->ID, '_is_tutor_instructor');
                delete_user_meta($user->ID, '_tutor_instructor_status');
                delete_user_meta($user->ID, '_tutor_instructor_approved');
                error_log("Role Custom: Superole kullanıcısı (ID: {$user->ID}) için eski meta veriler giriş sırasında temizlendi.");
            }
        }
    }

    /**
     * Superole olmayan kullanıcılar için eğitmen meta verilerini temizle
     * 36 eklentisindeki mantığı kullanarak kapsamlı temizleme yapar
     */
    public function cleanup_superole_instructor_meta($user_id) {
        // Tutor LMS aktif değilse çık
        if (!class_exists('TUTOR\Tutor')) {
            return;
        }

        // Kullanıcının gerçek bir eğitmen olup olmadığını kontrol et
        $user = get_userdata($user_id);
        if (!$user) {
            return;
        }

        // Superole rolü de yoksa ve gerçek eğitmen rolü de yoksa meta verileri temizle
        $has_superole = in_array(self::SUPEROLE_ROLE, $user->roles);
        $has_real_instructor_role = in_array('tutor_instructor', $user->roles);

        if (!$has_superole && !$has_real_instructor_role) {
            // 36 eklentisindeki gibi kapsamlı temizleme yap
            $this->clean_instructor_meta_data_comprehensive($user_id);

            error_log("Role Custom: Kullanıcı (ID: {$user_id}) için superole eğitmen meta verileri kapsamlı olarak temizlendi.");
        }
    }

    /**
     * 36 eklentisindeki mantığı kullanarak kapsamlı eğitmen meta veri temizleme
     * Tutor Utils remove_instructor_role metodunu kullanır
     */
    private function clean_instructor_meta_data_comprehensive($user_id) {
        // Kullanıcı ID'si geçerli mi kontrol et
        if (!$user_id || !get_userdata($user_id)) {
            return false;
        }

        // Tutor LMS'in kendi remove_instructor_role metodunu kullan
        if (function_exists('tutor_utils')) {
            tutor_utils()->remove_instructor_role($user_id);
        }

        // Eğitmen meta verilerini manuel olarak da temizle (güvenlik için)
        delete_user_meta($user_id, '_is_tutor_instructor');
        delete_user_meta($user_id, '_tutor_instructor_status');
        delete_user_meta($user_id, '_tutor_instructor_approved');

        // Kullanıcının rollerini kontrol et ve tutor_instructor rolünü kaldır
        $user = new WP_User($user_id);

        // Tutor'ın instructor_role değerini kullan
        if (function_exists('tutor') && isset(tutor()->instructor_role)) {
            $instructor_role = tutor()->instructor_role;
            if (in_array($instructor_role, $user->roles)) {
                $user->remove_role($instructor_role);
            }
        } else if (in_array('tutor_instructor', $user->roles)) {
            $user->remove_role('tutor_instructor');
        }

        // 36 eklentisindeki gibi tüm tutor instructor ile ilgili meta verileri temizle
        global $wpdb;
        $wpdb->query(
            $wpdb->prepare(
                "DELETE FROM {$wpdb->usermeta} WHERE user_id = %d AND meta_key LIKE %s",
                $user_id,
                '%_tutor_instructor%'
            )
        );

        return true;
    }

    /**
     * Global erişim için eğitmen meta verilerini temizleyen fonksiyon
     * 36 eklentisindeki clean_instructor_meta_data() fonksiyonunun aynısı
     */
    public function clean_instructor_meta_data($user_id) {
        return $this->clean_instructor_meta_data_comprehensive($user_id);
    }

    /**
     * Kullanıcı cache'lerini temizle
     */
    private function clear_user_caches($user_id) {
        // WordPress user cache'ini temizle
        clean_user_cache($user_id);

        // WordPress capabilities cache'ini temizle
        wp_cache_delete($user_id, 'user_meta');
        wp_cache_delete($user_id, 'users');

        // Tutor LMS cache'ini temizle (varsa)
        if (class_exists('TUTOR\TutorCache')) {
            // Tutor'ın instructor cache'lerini temizle
            $cache_keys = [
                "tutor_is_instructor_of_the_course_{$user_id}_*",
                "tutor_instructor_{$user_id}",
                "tutor_user_{$user_id}"
            ];

            foreach ($cache_keys as $pattern) {
                wp_cache_flush_group('tutor');
            }
        }

        // Object cache'i temizle
        if (function_exists('wp_cache_flush')) {
            wp_cache_flush();
        }
    }

    /**
     * Eski kalıcı meta veri sistemini temizle ve dinamik sisteme geç
     */
    public function cleanup_old_superole_instructor_system() {
        // Sadece admin kullanıcılar için ve sadece bir kez çalıştır
        if (!current_user_can('manage_options')) {
            return;
        }

        // Bu temizleme işlemini sadece bir kez yap
        $cleanup_done = get_option('role_custom_dynamic_instructor_cleanup_done', false);
        if ($cleanup_done) {
            return;
        }

        // Tutor LMS aktif değilse çık
        if (!class_exists('TUTOR\Tutor')) {
            update_option('role_custom_dynamic_instructor_cleanup_done', true);
            return;
        }

        // Tüm superole kullanıcılarını al
        $superole_users = get_users(['role' => self::SUPEROLE_ROLE]);
        $cleaned_count = 0;

        foreach ($superole_users as $user) {
            // Eğer kullanıcı gerçek tutor_instructor rolüne sahip değilse
            // (yani sadece superole için eklenmişse) meta verileri temizle
            $has_real_instructor_role = in_array('tutor_instructor', $user->roles);

            // Gerçek eğitmen rolü varsa meta verileri koru
            if ($has_real_instructor_role) {
                // Gerçek eğitmen, meta verileri koruyalım
                continue;
            }

            // Sadece superole için eklenen meta verileri temizle
            $had_meta = get_user_meta($user->ID, '_is_tutor_instructor', true);
            if ($had_meta) {
                delete_user_meta($user->ID, '_is_tutor_instructor');
                delete_user_meta($user->ID, '_tutor_instructor_status');
                delete_user_meta($user->ID, '_tutor_instructor_approved');
                $cleaned_count++;
            }
        }

        // İşlemin tamamlandığını kaydet
        update_option('role_custom_dynamic_instructor_cleanup_done', true);

        // Log kaydı
        error_log("Role Custom: Dinamik eğitmen sistemine geçiş tamamlandı. {$cleaned_count} kullanıcı için eski meta veriler temizlendi.");
    }



    /**
     * Superole kullanıcıları için tutor_instructor capability'sini ver
     */
    public function grant_tutor_instructor_cap_to_superole($allcaps, $caps, $args, $user) {
        // Sadece tutor_instructor capability kontrolü için
        if (!in_array('tutor_instructor', $caps)) {
            return $allcaps;
        }

        // Kullanıcı superole rolüne sahipse tutor_instructor capability'sini ver
        if ($user && in_array(self::SUPEROLE_ROLE, $user->roles)) {
            $allcaps['tutor_instructor'] = true;
        }

        return $allcaps;
    }

    /**
     * Superole kullanıcıları için Tutor LMS eğitmen meta verilerini dinamik olarak sağla
     */
    public function override_tutor_instructor_meta_for_superole($value, $object_id, $meta_key, $single) {
        // Sadece _is_tutor_instructor meta verisi için
        if ($meta_key !== '_is_tutor_instructor') {
            return $value;
        }

        // Kullanıcı bilgilerini al
        $user = get_userdata($object_id);
        if (!$user) {
            return $value;
        }

        // Eğer kullanıcı superole rolüne sahipse eğitmen olarak işaretle
        if (in_array(self::SUPEROLE_ROLE, $user->roles)) {
            // Tutor LMS aktif değilse çık
            if (!function_exists('tutor_time')) {
                return $value;
            }

            // Dinamik olarak eğitmen meta verisi döndür
            return $single ? tutor_time() : [tutor_time()];
        }

        // Superole değilse normal meta veriyi döndür
        return $value;
    }
    
    /**
     * Eklenti etkinleştirme
     */
    public function activate() {
        // Superole rolünü oluştur
        $this->create_superole_role();

        // WordPress capabilities cache'ini temizle
        if (function_exists('wp_cache_delete')) {
            wp_cache_delete('user_roles', 'options');
        }

        // Flush rewrite rules
        flush_rewrite_rules();

        // Aktivasyon zamanını kaydet
        update_option('role_custom_activated', current_time('mysql'));

        // Başarı bildirimi için transient ayarla
        set_transient('role_custom_activated_notice', true, 30);

        // Debug için log
        error_log('Role Custom: Eklenti etkinleştirildi ve Superole rolü oluşturuldu.');
    }
    
    /**
     * Eklenti devre dışı bırakma
     */
    public function deactivate() {
        $this->remove_superole_role();

        // Tutor LMS kurulum flag'ini temizle
        delete_option('role_custom_superole_instructor_setup_done');

        // Flush rewrite rules
        flush_rewrite_rules();

        // Deaktivasyon zamanını kaydet
        update_option('role_custom_deactivated', current_time('mysql'));
    }
    
    /**
     * Superole rolünü oluştur
     */
    private function create_superole_role() {
        // Önce mevcut rolü kaldır (eğer varsa)
        remove_role(self::SUPEROLE_ROLE);

        // Mevcut subscriber rolünün yetkilerini al
        $subscriber = get_role('subscriber');
        $capabilities = $subscriber ? $subscriber->capabilities : [];

        // Temel WordPress yetkileri ekle
        $capabilities = array_merge($capabilities, [
            'read' => true,
            'upload_files' => true,
            'edit_posts' => false,
            'edit_pages' => false,
            'publish_posts' => false,
            'publish_pages' => false,
            'delete_posts' => false,
            'delete_pages' => false,
        ]);
        
        // Tutor LMS yetkileri ekle - Instructor rolündeki tüm yetkiler
        $tutor_capabilities = [
            // Temel WordPress yetkileri (Instructor rolünden)
            'edit_posts' => true,
            'read' => true,
            'upload_files' => true,

            // Tutor LMS yönetim yetkileri
            'manage_tutor_instructor' => true,
            'manage_tutor' => true,
            'tutor_instructor' => true,

            // Kurs yetkileri
            'edit_tutor_course' => true,
            'read_tutor_course' => true,
            'delete_tutor_course' => true,
            'delete_tutor_courses' => true,
            'edit_tutor_courses' => true,
            'edit_others_tutor_courses' => true,
            'read_private_tutor_courses' => true,
            'publish_tutor_courses' => true,  // Kurs yayınlama yetkisi

            // Ders yetkileri
            'edit_tutor_lesson' => true,
            'read_tutor_lesson' => true,
            'delete_tutor_lesson' => true,
            'delete_tutor_lessons' => true,
            'edit_tutor_lessons' => true,
            'edit_others_tutor_lessons' => true,
            'read_private_tutor_lessons' => true,
            'publish_tutor_lessons' => true,

            // Quiz yetkileri
            'edit_tutor_quiz' => true,
            'read_tutor_quiz' => true,
            'delete_tutor_quiz' => true,
            'delete_tutor_quizzes' => true,
            'edit_tutor_quizzes' => true,
            'edit_others_tutor_quizzes' => true,
            'read_private_tutor_quizzes' => true,
            'publish_tutor_quizzes' => true,

            // Soru yetkileri
            'edit_tutor_question' => true,
            'read_tutor_question' => true,
            'delete_tutor_question' => true,
            'delete_tutor_questions' => true,
            'edit_tutor_questions' => true,
            'edit_others_tutor_questions' => true,
            'publish_tutor_questions' => true,
            'read_private_tutor_questions' => true,

            // Ek okuma yetkileri
            'read_course' => true,
            'read_lesson' => true,
            'read_quiz' => true,
            'read_question' => true,
            'read_announcement' => true,
        ];
        
        // WooCommerce yetkileri ekle - Tam erişim
        $woocommerce_capabilities = [
            // Temel WooCommerce yetkileri
            'manage_woocommerce' => true,
            'view_woocommerce_reports' => true,

            // Sipariş yönetimi
            'edit_shop_orders' => true,
            'edit_others_shop_orders' => true,
            'publish_shop_orders' => true,
            'read_shop_orders' => true,
            'delete_shop_orders' => true,
            'delete_others_shop_orders' => true,
            'read_private_shop_orders' => true,
            'edit_private_shop_orders' => true,
            'delete_private_shop_orders' => true,

            // Ürün yönetimi
            'edit_products' => true,
            'edit_others_products' => true,
            'publish_products' => true,
            'read_products' => true,
            'delete_products' => true,
            'delete_others_products' => true,
            'read_private_products' => true,
            'edit_private_products' => true,
            'delete_private_products' => true,

            // Ürün kategorileri ve etiketleri
            'manage_product_terms' => true,
            'edit_product_terms' => true,
            'delete_product_terms' => true,
            'assign_product_terms' => true,

            // Kupon yönetimi
            'edit_shop_coupons' => true,
            'edit_others_shop_coupons' => true,
            'publish_shop_coupons' => true,
            'read_shop_coupons' => true,
            'delete_shop_coupons' => true,
            'delete_others_shop_coupons' => true,
            'read_private_shop_coupons' => true,
            'edit_private_shop_coupons' => true,
            'delete_private_shop_coupons' => true,

            // Webhook yönetimi
            'edit_shop_webhooks' => true,
            'edit_others_shop_webhooks' => true,
            'publish_shop_webhooks' => true,
            'read_shop_webhooks' => true,
            'delete_shop_webhooks' => true,
            'delete_others_shop_webhooks' => true,
            'read_private_shop_webhooks' => true,
            'edit_private_shop_webhooks' => true,
            'delete_private_shop_webhooks' => true,
        ];
        
        // Tüm yetkileri birleştir
        $all_capabilities = array_merge($capabilities, $tutor_capabilities, $woocommerce_capabilities);
        
        // Rolü ekle
        $result = add_role(
            self::SUPEROLE_ROLE,
            __('Superole', 'role-custom'),
            $all_capabilities
        );

        // WordPress roles cache'ini temizle
        if (function_exists('wp_roles')) {
            wp_roles()->reinit();
        }

        // Global $wp_roles değişkenini sıfırla
        global $wp_roles;
        if (isset($wp_roles)) {
            $wp_roles = null;
        }

        // Log kaydı
        if ($result) {
            error_log('Role Custom: Superole rolü başarıyla oluşturuldu.');
        } else {
            error_log('Role Custom: Superole rolü oluşturulamadı!');
        }
    }
    
    /**
     * Superole rolünü kaldır
     */
    private function remove_superole_role() {
        // Önce bu role sahip kullanıcıları subscriber yap ve tutor instructor rolünü kaldır
        $users = get_users(['role' => self::SUPEROLE_ROLE]);
        foreach ($users as $user) {
            // Tutor instructor rolünü kaldır
            if (function_exists('tutor') && isset(tutor()->instructor_role)) {
                $instructor_role = tutor()->instructor_role;
            } else {
                $instructor_role = 'tutor_instructor';
            }

            if (in_array($instructor_role, $user->roles)) {
                $user->remove_role($instructor_role);
            }

            // Tutor eğitmen meta verilerini temizle
            delete_user_meta($user->ID, '_is_tutor_instructor');
            delete_user_meta($user->ID, '_tutor_instructor_status');
            delete_user_meta($user->ID, '_tutor_instructor_approved');

            // Subscriber rolüne geçir
            $user->set_role('subscriber');
        }

        // Rolü kaldır
        remove_role(self::SUPEROLE_ROLE);

        // WordPress roles cache'ini temizle
        if (function_exists('wp_roles')) {
            wp_roles()->reinit();
        }

        // Global $wp_roles değişkenini sıfırla
        global $wp_roles;
        if (isset($wp_roles)) {
            $wp_roles = null;
        }

        // Log kaydı
        error_log('Role Custom: Superole rolü kaldırıldı.');
    }
    
    /**
     * Tutor LMS menülerini kısıtla
     * Superole rolü tüm instructor yetkilerine sahip ancak belirli menüler gizlenir
     */
    public function restrict_tutor_menus() {
        // Sadece Superole rolündeki kullanıcılar için
        if (!$this->is_current_user_superole()) {
            return;
        }

        global $submenu;

        // Tutor LMS alt menülerini kontrol et
        if (isset($submenu['tutor'])) {
            $restricted_slugs = [
                'tutor-tools',              // Araçlar
                'tutor_settings',           // Ayarlar
                'tutor-withdrawals',        // Para Çekme Talepleri
                'tutor_withdraw',           // Para Çekme (alternatif slug)
                'withdraw',                 // Para Çekme (kısa slug)
                'tutor-get-pro'            // Upgrade to Pro
            ];

            foreach ($submenu['tutor'] as $key => $menu_item) {
                $menu_slug = $menu_item[2];

                // Kısıtlı menüler listesinde varsa kaldır
                if (in_array($menu_slug, $restricted_slugs)) {
                    unset($submenu['tutor'][$key]);
                }
            }
        }
    }
    
    /**
     * İzin verilen Tutor LMS menü slug'larını döndür
     */
    private function get_allowed_tutor_menu_slugs() {
        // Tutor LMS admin menü analizi sonucu doğru slug'lar
        return [
            'tutor',                           // Kurslar (ana sayfa)
            'tutor-students',                  // Öğrenciler (Students_List::STUDENTS_LIST_PAGE)
            'tutor_announcements',             // Duyurular
            'question_answer',                 // Q&A (Question_Answers_List::QUESTION_ANSWER_PAGE)
            'tutor_quiz_attempts'              // Sınav Denemeleri (Quiz_Attempts_List::QUIZ_ATTEMPT_PAGE)
        ];
    }

    /**
     * WooCommerce menülerini kısıtla
     */
    public function restrict_woocommerce_menus() {
        // Sadece Superole rolündeki kullanıcılar için
        if (!$this->is_current_user_superole()) {
            return;
        }

        global $menu, $submenu;

        // 1. Ödemeler ana sekmesini gizle (farklı olası slug'lar)
        $payment_menu_slugs = [
            'wc-admin&path=/payments/connect',
            'admin.php?page=wc-admin&path=/payments/connect',
            'wc-admin&path=/payments',
            'admin.php?page=wc-admin&path=/payments',
            'woocommerce-payments',
            'wc-payments',
            'admin.php?page=wc-settings&tab=checkout',
            'wc-settings&tab=checkout'
        ];

        foreach ($payment_menu_slugs as $slug) {
            $this->remove_top_level_menu($slug);
            // WordPress'in remove_menu_page fonksiyonunu da kullan
            remove_menu_page($slug);
        }

        // Spesifik ödemeler menüsünü kaldır
        remove_menu_page('admin.php?page=wc-settings&tab=checkout');
        remove_menu_page('wc-settings&tab=checkout');

        // WooCommerce ana menüsünü kaldır
        remove_menu_page('woocommerce');
        $this->remove_top_level_menu('woocommerce');

        // 2. WooCommerce alt menülerini kısıtla
        if (isset($submenu['woocommerce'])) {
            $restricted_wc_slugs = $this->get_restricted_woocommerce_menu_slugs();

            foreach ($submenu['woocommerce'] as $key => $menu_item) {
                $menu_slug = $menu_item[2];

                // Kısıtlı menüler listesinde varsa kaldır
                if (in_array($menu_slug, $restricted_wc_slugs)) {
                    unset($submenu['woocommerce'][$key]);
                }

                // Ana menü haline getirilen menüleri de alt menüden kaldır
                if (in_array($menu_slug, $this->get_top_level_menu_slugs())) {
                    unset($submenu['woocommerce'][$key]);
                }
            }
        }
    }

    /**
     * Kısıtlı WooCommerce menü slug'larını döndür
     */
    private function get_restricted_woocommerce_menu_slugs() {
        return [
            'wc-settings',                     // Ayarlar
            'wc-status',                       // Durum
            'wc-addons',                       // Genişletme Paketleri
        ];
    }

    /**
     * Ana menü haline getirilen WooCommerce menü slug'larını döndür
     */
    private function get_top_level_menu_slugs() {
        return [
            'admin.php?page=wc-orders',       // Siparişler
            'users.php?role=customer',        // Müşteriler
            'admin.php?page=wc-reports',      // Raporlar
            'edit.php?post_type=shop_coupon', // Kuponlar
        ];
    }

    /**
     * Top-level menüyü kaldır
     */
    private function remove_top_level_menu($menu_slug) {
        global $menu;

        foreach ($menu as $key => $menu_item) {
            if (isset($menu_item[2]) && $menu_item[2] === $menu_slug) {
                unset($menu[$key]);
                break;
            }
        }
    }

    /**
     * Ödemeler menüsünü özel olarak kaldır
     */
    public function remove_payments_menu() {
        // Sadece Superole rolündeki kullanıcılar için
        if (!$this->is_current_user_superole()) {
            return;
        }

        global $menu;

        // Tüm menüleri kontrol et ve ödemeler ile ilgili olanları kaldır
        foreach ($menu as $key => $menu_item) {
            if (isset($menu_item[2])) {
                $menu_slug = $menu_item[2];
                $menu_title = isset($menu_item[0]) ? $menu_item[0] : '';

                // Menü slug'ında veya başlığında ödemeler/payments geçiyorsa kaldır
                if (
                    strpos($menu_slug, 'payments') !== false ||
                    strpos($menu_slug, 'checkout') !== false ||
                    strpos($menu_slug, 'PAYMENTS_MENU_ITEM') !== false ||
                    strpos($menu_title, 'Ödemeler') !== false ||
                    strpos($menu_title, 'Payments') !== false
                ) {
                    unset($menu[$key]);
                }
            }
        }

        // WordPress'in remove_menu_page fonksiyonunu da kullan
        $payment_pages = [
            'admin.php?page=wc-settings&tab=checkout',
            'wc-settings&tab=checkout',
            'woocommerce-payments',
            'wc-payments'
        ];

        foreach ($payment_pages as $page) {
            remove_menu_page($page);
        }
    }

    /**
     * Pazarlama menüsünü gizle
     */
    public function hide_marketing_menu() {
        // Sadece Superole rolündeki kullanıcılar için
        if (!$this->is_current_user_superole()) {
            return;
        }

        // Pazarlama menüsünü kaldır
        remove_menu_page('woocommerce-marketing');

        // Alternatif pazarlama menü slug'ları
        $marketing_menu_slugs = [
            'woocommerce-marketing',
            'admin.php?page=wc-admin&path=/marketing',
            'wc-admin&path=/marketing',
            'marketing'
        ];

        foreach ($marketing_menu_slugs as $slug) {
            remove_menu_page($slug);
        }
    }

    /**
     * WooCommerce ürün kategorileri ve etiketleri menülerini gizle
     */
    public function hide_product_taxonomies() {
        // Sadece Superole rolündeki kullanıcılar için
        if (!$this->is_current_user_superole()) {
            return;
        }

        // WooCommerce aktif değilse çık
        if (!class_exists('WooCommerce')) {
            return;
        }

        global $submenu;

        // Ürünler menüsü altındaki kategoriler ve etiketler sekmelerini kaldır
        if (isset($submenu['edit.php?post_type=product'])) {
            foreach ($submenu['edit.php?post_type=product'] as $key => $menu_item) {
                $menu_slug = $menu_item[2];

                // Kategoriler ve etiketler menü slug'larını kontrol et
                if (
                    $menu_slug === 'edit-tags.php?taxonomy=product_cat&amp;post_type=product' ||
                    $menu_slug === 'edit-tags.php?taxonomy=product_cat&post_type=product' ||
                    $menu_slug === 'edit-tags.php?taxonomy=product_tag&amp;post_type=product' ||
                    $menu_slug === 'edit-tags.php?taxonomy=product_tag&post_type=product' ||
                    strpos($menu_slug, 'taxonomy=product_cat') !== false ||
                    strpos($menu_slug, 'taxonomy=product_tag') !== false
                ) {
                    unset($submenu['edit.php?post_type=product'][$key]);
                }
            }
        }

        // Doğrudan menü sayfalarını da kaldır
        remove_submenu_page('edit.php?post_type=product', 'edit-tags.php?taxonomy=product_cat&post_type=product');
        remove_submenu_page('edit.php?post_type=product', 'edit-tags.php?taxonomy=product_tag&post_type=product');
    }

    /**
     * WooCommerce alt menülerini ana menü haline getir
     */
    public function create_woocommerce_top_level_menus() {
        // Sadece Superole rolündeki kullanıcılar için
        if (!$this->is_current_user_superole()) {
            return;
        }

        // WooCommerce aktif değilse çık
        if (!class_exists('WooCommerce')) {
            return;
        }

        // 1. Siparişler ana menüsü
        add_menu_page(
            __('Siparişler', 'role-custom'),           // Sayfa başlığı
            __('Siparişler', 'role-custom'),           // Menü başlığı
            'edit_shop_orders',                        // Yetki
            'admin.php?page=wc-orders',                // Menü slug'ı (düzeltildi)
            '',                                        // Fonksiyon (boş bırak, redirect olacak)
            'dashicons-clipboard',                     // İkon
            56                                         // Pozisyon (WooCommerce'den sonra)
        );

        // 2. Müşteriler ana menüsü
        add_menu_page(
            __('Müşteriler', 'role-custom'),           // Sayfa başlığı
            __('Müşteriler', 'role-custom'),           // Menü başlığı
            'list_users',                              // Yetki
            'users.php?role=customer',                 // Menü slug'ı
            '',                                        // Fonksiyon (boş bırak, redirect olacak)
            'dashicons-groups',                        // İkon
            57                                         // Pozisyon
        );

        // 3. Raporlar ana menüsü
        add_menu_page(
            __('Raporlar', 'role-custom'),             // Sayfa başlığı
            __('Raporlar', 'role-custom'),             // Menü başlığı
            'view_woocommerce_reports',                // Yetki
            'admin.php?page=wc-reports',               // Menü slug'ı
            '',                                        // Fonksiyon (boş bırak, redirect olacak)
            'dashicons-chart-bar',                     // İkon
            58                                         // Pozisyon
        );

        // 4. Kuponlar ana menüsü
        add_menu_page(
            __('Kuponlar', 'role-custom'),             // Sayfa başlığı
            __('Kuponlar', 'role-custom'),             // Menü başlığı
            'edit_shop_coupons',                       // Yetki
            'edit.php?post_type=shop_coupon',          // Menü slug'ı
            '',                                        // Fonksiyon (boş bırak, redirect olacak)
            'dashicons-tickets-alt',                   // İkon
            59                                         // Pozisyon
        );
    }

    /**
     * Dashboard'dan Tutor LMS'e yönlendir
     */
    public function redirect_dashboard_to_tutor() {
        // Sadece Superole rolündeki kullanıcılar için
        if (!$this->is_current_user_superole()) {
            return;
        }

        // Sadece admin panelinde
        if (!is_admin()) {
            return;
        }

        // Dashboard sayfasında mıyız kontrol et
        global $pagenow;
        if ($pagenow === 'index.php' && !isset($_GET['page'])) {
            // Tutor LMS aktif mi kontrol et
            if (class_exists('TUTOR\Tutor')) {
                // Tutor LMS ana sayfasına yönlendir
                wp_redirect(admin_url('admin.php?page=tutor'));
                exit;
            }
        }
    }
    
    /**
     * Mevcut kullanıcının Superole rolünde olup olmadığını kontrol et
     */
    private function is_current_user_superole() {
        $current_user = wp_get_current_user();
        return in_array(self::SUPEROLE_ROLE, $current_user->roles);
    }
    
    /**
     * WooCommerce ve Tutor LMS yetkilerini ayarla
     */
    public function setup_woocommerce_capabilities() {
        // Superole rolü için WooCommerce ve Tutor LMS yetkilerini kontrol et ve gerekirse ekle
        $role = get_role(self::SUPEROLE_ROLE);
        if ($role) {
            // Tutor LMS yetkileri - Instructor rolündeki tüm yetkiler
            $tutor_caps = [
                // Temel WordPress yetkileri
                'edit_posts',
                'read',
                'upload_files',

                // Tutor LMS yönetim yetkileri
                'manage_tutor_instructor',
                'manage_tutor',
                'tutor_instructor',

                // Kurs yetkileri
                'edit_tutor_course',
                'read_tutor_course',
                'delete_tutor_course',
                'delete_tutor_courses',
                'edit_tutor_courses',
                'edit_others_tutor_courses',
                'read_private_tutor_courses',
                'publish_tutor_courses',

                // Ders yetkileri
                'edit_tutor_lesson',
                'read_tutor_lesson',
                'delete_tutor_lesson',
                'delete_tutor_lessons',
                'edit_tutor_lessons',
                'edit_others_tutor_lessons',
                'read_private_tutor_lessons',
                'publish_tutor_lessons',

                // Quiz yetkileri
                'edit_tutor_quiz',
                'read_tutor_quiz',
                'delete_tutor_quiz',
                'delete_tutor_quizzes',
                'edit_tutor_quizzes',
                'edit_others_tutor_quizzes',
                'read_private_tutor_quizzes',
                'publish_tutor_quizzes',

                // Soru yetkileri
                'edit_tutor_question',
                'read_tutor_question',
                'delete_tutor_question',
                'delete_tutor_questions',
                'edit_tutor_questions',
                'edit_others_tutor_questions',
                'publish_tutor_questions',
                'read_private_tutor_questions',

                // Ek okuma yetkileri
                'read_course',
                'read_lesson',
                'read_quiz',
                'read_question',
                'read_announcement',
            ];

            // WooCommerce yetkileri
            $woocommerce_caps = [
                // Temel WooCommerce yetkileri
                'manage_woocommerce',
                'view_woocommerce_reports',

                // Sipariş yönetimi
                'edit_shop_orders',
                'edit_others_shop_orders',
                'publish_shop_orders',
                'read_shop_orders',
                'delete_shop_orders',
                'delete_others_shop_orders',
                'read_private_shop_orders',
                'edit_private_shop_orders',
                'delete_private_shop_orders',

                // Ürün yönetimi
                'edit_products',
                'edit_others_products',
                'publish_products',
                'read_products',
                'delete_products',
                'delete_others_products',
                'read_private_products',
                'edit_private_products',
                'delete_private_products',

                // Ürün kategorileri ve etiketleri
                'manage_product_terms',
                'edit_product_terms',
                'delete_product_terms',
                'assign_product_terms',

                // Kupon yönetimi
                'edit_shop_coupons',
                'edit_others_shop_coupons',
                'publish_shop_coupons',
                'read_shop_coupons',
                'delete_shop_coupons',
                'delete_others_shop_coupons',
                'read_private_shop_coupons',
                'edit_private_shop_coupons',
                'delete_private_shop_coupons',

                // Webhook yönetimi
                'edit_shop_webhooks',
                'edit_others_shop_webhooks',
                'publish_shop_webhooks',
                'read_shop_webhooks',
                'delete_shop_webhooks',
                'delete_others_shop_webhooks',
                'read_private_shop_webhooks',
                'edit_private_shop_webhooks',
                'delete_private_shop_webhooks',
            ];

            // Tüm yetkileri ekle
            $all_caps = array_merge($tutor_caps, $woocommerce_caps);
            foreach ($all_caps as $cap) {
                if (!$role->has_cap($cap)) {
                    $role->add_cap($cap);
                }
            }
        }
    }
    
    /**
     * Kısıtlı menüleri gizlemek için CSS
     */
    public function hide_restricted_menus_css() {
        if (!$this->is_current_user_superole()) {
            return;
        }

        // Tutor LMS ve WooCommerce menuleri icin CSS
        echo '<style>
            /* Tutor LMS restricted menus - Hide for Superole role */
            #adminmenu .wp-submenu li a[href*="tutor-new-feature"],
            #adminmenu .wp-submenu li a[href*="create-course"],
            #adminmenu .wp-submenu li a[href*="tutor-themes"],
            #adminmenu .wp-submenu li a[href*="course-category"],
            #adminmenu .wp-submenu li a[href*="course-tag"],
            #adminmenu .wp-submenu li a[href*="tutor-instructors"],
            #adminmenu .wp-submenu li a[href*="tutor-addons"],
            #adminmenu .wp-submenu li a[href*="tutor-tools"],
            #adminmenu .wp-submenu li a[href*="tutor_settings"],
            #adminmenu .wp-submenu li a[href*="tutor-get-pro"],
            #adminmenu .wp-submenu li a[href*="tutor_orders"],
            #adminmenu .wp-submenu li a[href*="tutor_coupons"],
            #adminmenu .wp-submenu li a[href*="tutor-withdrawals"],
            #adminmenu .wp-submenu li a[href*="tutor_withdraw"],
            #adminmenu .wp-submenu li a[href*="withdraw"],

            /* WooCommerce restricted menus - Hide for Superole role */
            #adminmenu .wp-submenu li a[href*="wc-settings"],
            #adminmenu .wp-submenu li a[href*="wc-status"],
            #adminmenu .wp-submenu li a[href*="wc-addons"],

            /* Hide Payments main menu - Various possible slugs */
            #adminmenu li a[href*="payments/connect"],
            #adminmenu li a[href*="wc-admin&path=/payments"],
            #adminmenu li a[href*="woocommerce-payments"],
            #adminmenu li a[href*="wc-payments"],
            #adminmenu li a[href*="wc-settings&tab=checkout"],
            #adminmenu li.toplevel_page_wc-admin-path-payments,
            #adminmenu li[id*="payments"],
            #toplevel_page_admin-page-wc-settings-tab-checkout-from-PAYMENTS_MENU_ITEM,
            #adminmenu #toplevel_page_admin-page-wc-settings-tab-checkout-from-PAYMENTS_MENU_ITEM,
            li[id*="checkout"],
            li[id*="PAYMENTS_MENU_ITEM"],
            #adminmenu li[class*="checkout"],
            #adminmenu li[class*="payments"],

            /* Additional specific WooCommerce menu items to hide */
            #toplevel_page_woocommerce > ul > li.wp-first-item > a,
            #toplevel_page_woocommerce > ul > li:nth-child(6),
            #toplevel_page_woocommerce-marketing > ul > li.wp-first-item.current > a,

            /* Hide WooCommerce submenu items by href content */
            #adminmenu #toplevel_page_woocommerce ul li a[href*="wc-settings"],
            #adminmenu #toplevel_page_woocommerce ul li a[href*="wc-status"],
            #adminmenu #toplevel_page_woocommerce ul li a[href*="wc-addons"],

            /* Hide submenu items that are now top-level menus */
            #adminmenu #toplevel_page_woocommerce ul li a[href*="edit.php?post_type=shop_order"],
            #adminmenu #toplevel_page_woocommerce ul li a[href*="shop_order"],
            #adminmenu #toplevel_page_woocommerce ul li a[href*="wc-orders"],
            #adminmenu #toplevel_page_woocommerce ul li a[href*="wc-reports"],
            #adminmenu #toplevel_page_woocommerce ul li a[href*="users.php?role=customer"],
            #adminmenu #toplevel_page_woocommerce ul li a[href*="edit.php?post_type=shop_coupon"],
            #adminmenu #toplevel_page_woocommerce ul li a[href*="shop_coupon"],

            /* Hide from Marketing submenu */
            #adminmenu #toplevel_page_woocommerce-marketing ul li a[href*="edit.php?post_type=shop_coupon"],
            #adminmenu #toplevel_page_woocommerce-marketing ul li a[href*="shop_coupon"],

            /* Hide WooCommerce main menu */
            #toplevel_page_woocommerce > a > div.wp-menu-name,
            #toplevel_page_woocommerce > a,
            #toplevel_page_woocommerce,

            /* Hide WordPress Tools menu */
            #menu-tools > a,
            #menu-tools,

            /* Hide WordPress Dashboard menu */
            #menu-dashboard > a,
            #menu-dashboard,

            /* Hide WordPress Users menu */
            #menu-users > a,
            #menu-users,

            /* Hide Marketing menu */
            #toplevel_page_woocommerce-marketing > a,
            #toplevel_page_woocommerce-marketing,
            #adminmenu li a[href*="woocommerce-marketing"],
            #adminmenu li a[href*="wc-admin&path=/marketing"],
            #adminmenu li a[href*="marketing"],

            /* Hide WooCommerce Product Categories and Tags menus */
            #adminmenu .wp-submenu li a[href*="taxonomy=product_cat"],
            #adminmenu .wp-submenu li a[href*="taxonomy=product_tag"],
            #adminmenu #toplevel_page_edit-post_type-product ul li a[href*="taxonomy=product_cat"],
            #adminmenu #toplevel_page_edit-post_type-product ul li a[href*="taxonomy=product_tag"],

            /* Hide specific menu items that create empty spaces */
            #adminmenu > li:nth-child(10),
            #adminmenu > li:nth-child(3) {
                display: none !important;
            }

            /* Fix active menu styling for custom top-level menus */
            #adminmenu li.menu-top.toplevel_page_admin-page-wc-orders.current > a,
            #adminmenu li.menu-top.toplevel_page_admin-page-wc-orders.wp-has-current-submenu > a,
            #adminmenu li.menu-top.toplevel_page_users-php-role-customer.current > a,
            #adminmenu li.menu-top.toplevel_page_users-php-role-customer.wp-has-current-submenu > a,
            #adminmenu li.menu-top.toplevel_page_admin-page-wc-reports.current > a,
            #adminmenu li.menu-top.toplevel_page_admin-page-wc-reports.wp-has-current-submenu > a,
            #adminmenu li.menu-top.toplevel_page_edit-php-post_type-shop_coupon.current > a,
            #adminmenu li.menu-top.toplevel_page_edit-php-post_type-shop_coupon.wp-has-current-submenu > a {
                background-color: #0073aa !important;
                color: #fff !important;
            }

            /* Active menu arrow for custom menus */
            #adminmenu li.menu-top.toplevel_page_admin-page-wc-orders.current:after,
            #adminmenu li.menu-top.toplevel_page_admin-page-wc-orders.wp-has-current-submenu:after,
            #adminmenu li.menu-top.toplevel_page_users-php-role-customer.current:after,
            #adminmenu li.menu-top.toplevel_page_users-php-role-customer.wp-has-current-submenu:after,
            #adminmenu li.menu-top.toplevel_page_admin-page-wc-reports.current:after,
            #adminmenu li.menu-top.toplevel_page_admin-page-wc-reports.wp-has-current-submenu:after,
            #adminmenu li.menu-top.toplevel_page_edit-php-post_type-shop_coupon.current:after,
            #adminmenu li.menu-top.toplevel_page_edit-php-post_type-shop_coupon.wp-has-current-submenu:after {
                border-right-color: #f1f1f1 !important;
            }
        </style>';

        // JavaScript ile dinamik olarak ödemeler menüsünü gizle
        echo '<script>
            jQuery(document).ready(function($) {
                // Ödemeler menüsünü gizle
                $("#toplevel_page_admin-page-wc-settings-tab-checkout-from-PAYMENTS_MENU_ITEM").hide();
                $("li[id*=\"checkout\"]").hide();
                $("li[id*=\"PAYMENTS_MENU_ITEM\"]").hide();
                $("li[id*=\"payments\"]").hide();
                $("a[href*=\"wc-settings&tab=checkout\"]").parent().hide();
                $("a[href*=\"payments\"]").parent().hide();

                // Spesifik WooCommerce menü öğelerini gizle
                $("#toplevel_page_woocommerce > ul > li.wp-first-item > a").hide();
                $("#toplevel_page_woocommerce > ul > li:nth-child(6)").hide();
                $("#toplevel_page_woocommerce-marketing > ul > li.wp-first-item.current > a").hide();

                // WooCommerce alt menülerinde belirli öğeleri gizle
                $("#toplevel_page_woocommerce ul li").each(function() {
                    var $link = $(this).find("a");
                    var href = $link.attr("href") || "";
                    var text = $link.text().trim();

                    // Ayarlar, Durum, Genişletme Paketleri menülerini gizle
                    if (href.includes("wc-settings") ||
                        href.includes("wc-status") ||
                        href.includes("wc-addons") ||
                        text === "Ayarlar" ||
                        text === "Settings" ||
                        text === "Durum" ||
                        text === "Status" ||
                        text === "Genişletme Paketleri" ||
                        text === "Extensions") {
                        $(this).hide();
                    }

                    // Ana menü haline getirilen alt menüleri gizle
                    if (href.includes("shop_order") ||
                        href.includes("wc-orders") ||
                        href.includes("wc-reports") ||
                        href.includes("users.php?role=customer") ||
                        href.includes("shop_coupon") ||
                        text === "Siparişler" ||
                        text === "Orders" ||
                        text === "Müşteriler" ||
                        text === "Customers" ||
                        text === "Raporlar" ||
                        text === "Reports" ||
                        text === "Kuponlar" ||
                        text === "Coupons") {
                        $(this).hide();
                    }
                });

                // WooCommerce ana menüsünü gizle
                $("#toplevel_page_woocommerce").hide();
                $("#toplevel_page_woocommerce > a").hide();
                $("#toplevel_page_woocommerce > a > div.wp-menu-name").hide();

                // WordPress Araçlar menüsünü gizle
                $("#menu-tools").hide();
                $("#menu-tools > a").hide();

                // WordPress Dashboard menüsünü gizle
                $("#menu-dashboard").hide();
                $("#menu-dashboard > a").hide();

                // WordPress Kullanıcılar menüsünü gizle
                $("#menu-users").hide();
                $("#menu-users > a").hide();

                // Pazarlama menüsünü gizle
                $("#toplevel_page_woocommerce-marketing").hide();
                $("#toplevel_page_woocommerce-marketing > a").hide();
                $("a[href*=\'woocommerce-marketing\']").parent().hide();
                $("a[href*=\'wc-admin&path=/marketing\']").parent().hide();

                // WooCommerce ürün kategorileri ve etiketleri menülerini gizle
                $("a[href*=\'taxonomy=product_cat\']").parent().hide();
                $("a[href*=\'taxonomy=product_tag\']").parent().hide();
                $("#toplevel_page_edit-post_type-product ul li a[href*=\'taxonomy=product_cat\']").parent().hide();
                $("#toplevel_page_edit-post_type-product ul li a[href*=\'taxonomy=product_tag\']").parent().hide();

                // Boşluk oluşturan belirli menü öğelerini gizle
                $("#adminmenu > li:nth-child(10)").hide();
                $("#adminmenu > li:nth-child(3)").hide();

                // Tutor LMS para çekme menülerini gizle
                $("a[href*=\'tutor-withdrawals\']").parent().hide();
                $("a[href*=\'tutor_withdraw\']").parent().hide();
                $("a[href*=\'withdraw\']").parent().hide();

                // Menü metni ile para çekme, pazarlama ve taksonomi menülerini gizle
                $("#adminmenu a").each(function() {
                    var text = $(this).text().trim();
                    if (text === "Para Çekme Talepleri" ||
                        text === "Withdrawals" ||
                        text === "Para Çekme" ||
                        text === "Withdraw" ||
                        text === "Ödemeler" ||
                        text === "Payments" ||
                        text === "Pazarlama" ||
                        text === "Marketing" ||
                        text === "Kategoriler" ||
                        text === "Categories" ||
                        text === "Etiketler" ||
                        text === "Tags" ||
                        text === "Product categories" ||
                        text === "Product tags") {
                        $(this).parent().hide();
                    }
                });

                // Aktif menü stilini düzelt
                function fixActiveMenuStyling() {
                    var currentUrl = window.location.href;

                    // Siparişler menüsü için
                    if (currentUrl.includes("page=wc-orders")) {
                        $("#adminmenu li").removeClass("current wp-has-current-submenu");
                        var ordersMenu = $("#adminmenu").find("a[href*=\'wc-orders\']").closest("li");
                        ordersMenu.addClass("current wp-has-current-submenu");
                    }

                    // Müşteriler menüsü için
                    if (currentUrl.includes("users.php") && currentUrl.includes("role=customer")) {
                        $("#adminmenu li").removeClass("current wp-has-current-submenu");
                        var customersMenu = $("#adminmenu").find("a[href*=\'role=customer\']").closest("li");
                        customersMenu.addClass("current wp-has-current-submenu");
                    }

                    // Raporlar menüsü için
                    if (currentUrl.includes("page=wc-reports")) {
                        $("#adminmenu li").removeClass("current wp-has-current-submenu");
                        var reportsMenu = $("#adminmenu").find("a[href*=\'wc-reports\']").closest("li");
                        reportsMenu.addClass("current wp-has-current-submenu");
                    }

                    // Kuponlar menüsü için
                    if (currentUrl.includes("post_type=shop_coupon")) {
                        $("#adminmenu li").removeClass("current wp-has-current-submenu");
                        var couponsMenu = $("#adminmenu").find("a[href*=\'shop_coupon\']").closest("li");
                        couponsMenu.addClass("current wp-has-current-submenu");
                    }
                }

                // Sayfa yüklendiğinde ve AJAX sonrasında çalıştır
                fixActiveMenuStyling();
                $(document).ajaxComplete(function() {
                    fixActiveMenuStyling();
                });
            });
        </script>';
    }
    
    /**
     * Kullanıcı yetkilerini filtrele
     */
    public function filter_user_capabilities($allcaps, $caps, $args, $user) {
        // Sadece Superole rolündeki kullanıcılar için
        if (!in_array(self::SUPEROLE_ROLE, $user->roles)) {
            return $allcaps;
        }

        // Tutor LMS ve WooCommerce yetkilerini garanti et
        $guaranteed_caps = [
            // Tutor LMS yetkileri - Instructor rolündeki tüm yetkiler
            'edit_posts' => true,
            'read' => true,
            'upload_files' => true,
            'manage_tutor_instructor' => true,
            'manage_tutor' => true,
            'tutor_instructor' => true,

            // Kurs yetkileri
            'edit_tutor_course' => true,
            'read_tutor_course' => true,
            'delete_tutor_course' => true,
            'delete_tutor_courses' => true,
            'edit_tutor_courses' => true,
            'edit_others_tutor_courses' => true,
            'read_private_tutor_courses' => true,
            'publish_tutor_courses' => true,

            // Ders yetkileri
            'edit_tutor_lesson' => true,
            'read_tutor_lesson' => true,
            'delete_tutor_lesson' => true,
            'delete_tutor_lessons' => true,
            'edit_tutor_lessons' => true,
            'edit_others_tutor_lessons' => true,
            'read_private_tutor_lessons' => true,
            'publish_tutor_lessons' => true,

            // Quiz yetkileri
            'edit_tutor_quiz' => true,
            'read_tutor_quiz' => true,
            'delete_tutor_quiz' => true,
            'delete_tutor_quizzes' => true,
            'edit_tutor_quizzes' => true,
            'edit_others_tutor_quizzes' => true,
            'read_private_tutor_quizzes' => true,
            'publish_tutor_quizzes' => true,

            // Soru yetkileri
            'edit_tutor_question' => true,
            'read_tutor_question' => true,
            'delete_tutor_question' => true,
            'delete_tutor_questions' => true,
            'edit_tutor_questions' => true,
            'edit_others_tutor_questions' => true,
            'publish_tutor_questions' => true,
            'read_private_tutor_questions' => true,

            // Ek okuma yetkileri
            'read_course' => true,
            'read_lesson' => true,
            'read_quiz' => true,
            'read_question' => true,
            'read_announcement' => true,

            // WooCommerce yetkileri - Tam erişim
            'manage_woocommerce' => true,
            'view_woocommerce_reports' => true,
            'edit_shop_orders' => true,
            'edit_others_shop_orders' => true,
            'edit_products' => true,
            'edit_others_products' => true,
            'manage_product_terms' => true,
            'edit_shop_coupons' => true,
            'edit_others_shop_coupons' => true,
        ];

        return array_merge($allcaps, $guaranteed_caps);
    }
    
    /**
     * Admin bildirimleri
     */
    public function admin_notices() {
        // Eklenti etkinleştirildikten sonra başarı mesajı
        if (get_transient('role_custom_activated_notice')) {
            $role_exists = get_role(self::SUPEROLE_ROLE) ? 'Evet' : 'Hayır';
            echo '<div class="notice notice-success is-dismissible">';
            echo '<p><strong>' . __('Role Custom:', 'role-custom') . '</strong> ';
            echo __('Eklenti başarıyla etkinleştirildi. Superole rolü oluşturuldu.', 'role-custom');
            echo '<br><small>Debug: Superole rolü mevcut mu? ' . $role_exists . '</small>';
            echo '</p></div>';
            delete_transient('role_custom_activated_notice');
        }

        // Superole rolü eksikse hata mesajı
        if (!get_role(self::SUPEROLE_ROLE) && current_user_can('manage_options')) {
            echo '<div class="notice notice-error">';
            echo '<p><strong>' . __('Role Custom:', 'role-custom') . '</strong> ';
            echo __('Superole rolü bulunamadı! Eklentiyi devre dışı bırakıp tekrar etkinleştirin.', 'role-custom');
            echo ' <a href="' . admin_url('plugins.php') . '">Eklentiler sayfasına git</a>';
            echo '</p></div>';
        }

        // Tutor LMS veya WooCommerce eksikse uyarı
        if (!class_exists('TUTOR\Tutor')) {
            echo '<div class="notice notice-warning">';
            echo '<p><strong>' . __('Role Custom:', 'role-custom') . '</strong> ';
            echo __('Tutor LMS eklentisi bulunamadı. Menü kısıtlamaları çalışmayabilir.', 'role-custom');
            echo '</p></div>';
        } else {
            // Tutor LMS eğitmen kurulum durumu kontrolü
            $setup_done = get_option('role_custom_superole_instructor_setup_done', false);
            if ($setup_done && current_user_can('manage_options')) {
                $superole_users = get_users(['role' => self::SUPEROLE_ROLE]);
                $user_count = count($superole_users);
                if ($user_count > 0) {
                    echo '<div class="notice notice-info is-dismissible">';
                    echo '<p><strong>' . __('Role Custom:', 'role-custom') . '</strong> ';
                    echo sprintf(__('%d superole kullanıcısı için Tutor LMS eğitmen yetkileri başarıyla kuruldu.', 'role-custom'), $user_count);
                    echo '</p></div>';
                }
            }
        }

        if (!class_exists('WooCommerce')) {
            echo '<div class="notice notice-warning">';
            echo '<p><strong>' . __('Role Custom:', 'role-custom') . '</strong> ';
            echo __('WooCommerce eklentisi bulunamadı. WooCommerce yetkileri çalışmayabilir.', 'role-custom');
            echo '</p></div>';
        }
    }

    /**
     * Rol kontrolü ve gerekirse oluşturma
     */
    public function check_and_create_role() {
        // Sadece admin kullanıcılar için ve sadece bir kez çalıştır
        if (!current_user_can('manage_options')) {
            return;
        }

        // Eğer rol yoksa oluştur
        if (!get_role(self::SUPEROLE_ROLE)) {
            $this->create_superole_role();

            // Bildirim ekle
            add_action('admin_notices', function() {
                echo '<div class="notice notice-info is-dismissible">';
                echo '<p><strong>Role Custom:</strong> Superole rolü eksikti ve yeniden oluşturuldu.</p>';
                echo '</div>';
            });
        }
    }

    /**
     * Dil dosyalarını yükle
     */
    public function load_textdomain() {
        load_plugin_textdomain(
            'role-custom',
            false,
            dirname(ROLE_CUSTOM_PLUGIN_BASENAME) . '/languages'
        );
    }

    /**
     * WooCommerce verilerini superole kullanıcıları için filtrele
     */
    public function filter_woocommerce_data_for_superole($query) {
        // Sadece admin panelinde çalışsın
        if (!is_admin()) {
            return;
        }

        // Sadece superole rolündeki kullanıcılar için
        if (!$this->is_current_user_superole()) {
            return;
        }

        // Ana sorgu değilse çık
        if (!$query->is_main_query()) {
            return;
        }

        // Mevcut kullanıcı ID'sini al
        $current_user_id = get_current_user_id();

        // Ürünler sayfası için filtreleme - Sadece liste görünümünde
        if ($query->get('post_type') === 'product') {
            // Sadece edit.php sayfasında (liste görünümü) filtreleme yap
            global $pagenow;
            if ($pagenow === 'edit.php' && !isset($_GET['action'])) {
                $query->set('author', $current_user_id);
            }
        }

        // Kuponlar sayfası için filtreleme
        if ($query->get('post_type') === 'shop_coupon') {
            $query->set('author', $current_user_id);
        }
    }

    /**
     * Siparişleri kullanıcının ürünlerine göre filtrele
     */
    public function filter_orders_by_user_products($where, $query) {
        global $wpdb;

        // Sadece admin panelinde çalışsın
        if (!is_admin()) {
            return $where;
        }

        // Sadece superole rolündeki kullanıcılar için
        if (!$this->is_current_user_superole()) {
            return $where;
        }

        // Ana sorgu değilse çık
        if (!$query->is_main_query()) {
            return $where;
        }

        // Siparişler sayfası kontrolü - daha geniş kontrol
        $post_type = $query->get('post_type');
        if (!in_array($post_type, ['shop_order', 'wc_order']) && !$this->is_orders_page()) {
            return $where;
        }

        // Mevcut kullanıcı ID'sini al
        $current_user_id = get_current_user_id();

        // Kullanıcının ürünlerini al
        $user_products = $this->get_user_product_ids($current_user_id);

        if (empty($user_products)) {
            // Kullanıcının hiç ürünü yoksa hiçbir sipariş gösterme
            $where .= " AND 1=0";
            return $where;
        }

        // Ürün ID'lerini string olarak hazırla
        $product_ids = implode(',', array_map('intval', $user_products));

        // Siparişleri kullanıcının ürünlerine göre filtrele
        $order_filter = " AND {$wpdb->posts}.ID IN (
            SELECT DISTINCT order_items.order_id
            FROM {$wpdb->prefix}woocommerce_order_items as order_items
            INNER JOIN {$wpdb->prefix}woocommerce_order_itemmeta as order_item_meta
                ON order_items.order_item_id = order_item_meta.order_item_id
            WHERE order_item_meta.meta_key = '_product_id'
                AND order_item_meta.meta_value IN ({$product_ids})
        )";

        $where .= $order_filter;

        return $where;
    }

    /**
     * HPOS siparişlerini kullanıcının ürünlerine göre filtrele
     */
    public function filter_hpos_orders_by_user_products($clauses, $query) {
        global $wpdb;

        // Sadece admin panelinde çalışsın
        if (!is_admin()) {
            return $clauses;
        }

        // Sadece superole rolündeki kullanıcılar için
        if (!$this->is_current_user_superole()) {
            return $clauses;
        }

        // Mevcut kullanıcı ID'sini al
        $current_user_id = get_current_user_id();

        // Kullanıcının ürünlerini al
        $user_products = $this->get_user_product_ids($current_user_id);

        if (empty($user_products)) {
            // Kullanıcının hiç ürünü yoksa hiçbir sipariş gösterme
            $clauses['where'] .= " AND 1=0";
            return $clauses;
        }

        // Ürün ID'lerini string olarak hazırla
        $product_ids = implode(',', array_map('intval', $user_products));

        // HPOS tabloları için sipariş filtreleme - Doğru tablo adı
        $orders_table = $wpdb->prefix . 'wc_orders';
        $hpos_filter = " AND {$orders_table}.id IN (
            SELECT DISTINCT order_items.order_id
            FROM {$wpdb->prefix}woocommerce_order_items as order_items
            INNER JOIN {$wpdb->prefix}woocommerce_order_itemmeta as order_item_meta
                ON order_items.order_item_id = order_item_meta.order_item_id
            WHERE order_item_meta.meta_key = '_product_id'
                AND order_item_meta.meta_value IN ({$product_ids})
        )";

        $clauses['where'] .= $hpos_filter;

        return $clauses;
    }

    /**
     * WooCommerce admin sayfalarında ek filtreleme ekle
     */
    public function add_woocommerce_admin_filters() {
        // Sadece superole rolündeki kullanıcılar için
        if (!$this->is_current_user_superole()) {
            return;
        }

        global $typenow;



        // Ürünler sayfası için ek filtreleme
        if ($typenow === 'product') {
            add_filter('views_edit-product', [$this, 'filter_product_views']);
        }

        // Kuponlar sayfası için ek filtreleme
        if ($typenow === 'shop_coupon') {
            add_filter('views_edit-shop_coupon', [$this, 'filter_coupon_views']);
        }

        // Siparişler sayfası için ek filtreleme - daha geniş kontrol
        if ($typenow === 'shop_order' || $typenow === 'wc_order' || $this->is_orders_page()) {
            add_filter('views_edit-shop_order', [$this, 'filter_order_views']);
        }
    }

    /**
     * WooCommerce ürün row actions'larını düzelt
     * Superole kullanıcıları için düzenle ve hızlı düzenle seçeneklerini geri ekle
     */
    public function fix_product_row_actions($actions, $post) {
        // Sadece superole rolündeki kullanıcılar için
        if (!$this->is_current_user_superole()) {
            return $actions;
        }

        // Sadece ürünler için
        if ($post->post_type !== 'product') {
            return $actions;
        }

        // Sadece admin panelinde
        if (!is_admin()) {
            return $actions;
        }

        // Superole kullanıcıları için tüm ürün row actions'larını yeniden oluştur
        $new_actions = array();

        // Düzenle linki - her zaman ekle
        $new_actions['edit'] = sprintf(
            '<a href="%s" aria-label="%s">%s</a>',
            get_edit_post_link($post->ID),
            /* translators: %s: Post title. */
            esc_attr(sprintf(__('Edit &#8220;%s&#8221;'), $post->post_title)),
            __('Edit')
        );

        // Hızlı düzenle linki - her zaman ekle
        $new_actions['inline hide-if-no-js'] = sprintf(
            '<button type="button" class="button-link editinline" aria-label="%s" aria-expanded="false">%s</button>',
            /* translators: %s: Post title. */
            esc_attr(sprintf(__('Quick edit &#8220;%s&#8221; inline'), $post->post_title)),
            __('Quick Edit')
        );

        // Çöp kutusuna taşı/geri yükle linki
        if ($post->post_status === 'trash') {
            $new_actions['untrash'] = sprintf(
                '<a href="%s" aria-label="%s">%s</a>',
                wp_nonce_url(admin_url('edit.php?post_type=product&action=untrash&post=' . $post->ID), 'untrash-post_' . $post->ID),
                /* translators: %s: Post title. */
                esc_attr(sprintf(__('Restore &#8220;%s&#8221; from the Trash'), $post->post_title)),
                __('Restore')
            );

            $new_actions['delete'] = sprintf(
                '<a href="%s" class="submitdelete" aria-label="%s">%s</a>',
                wp_nonce_url(admin_url('edit.php?post_type=product&action=delete&post=' . $post->ID), 'delete-post_' . $post->ID),
                /* translators: %s: Post title. */
                esc_attr(sprintf(__('Delete &#8220;%s&#8221; permanently'), $post->post_title)),
                __('Delete Permanently')
            );
        } else {
            $new_actions['trash'] = sprintf(
                '<a href="%s" class="submitdelete" aria-label="%s">%s</a>',
                get_delete_post_link($post->ID),
                /* translators: %s: Post title. */
                esc_attr(sprintf(__('Move &#8220;%s&#8221; to the Trash'), $post->post_title)),
                __('Trash')
            );
        }

        // Görüntüle linki
        if ($post->post_status === 'publish') {
            $new_actions['view'] = sprintf(
                '<a href="%s" rel="bookmark" aria-label="%s">%s</a>',
                get_permalink($post->ID),
                /* translators: %s: Post title. */
                esc_attr(sprintf(__('View &#8220;%s&#8221;'), $post->post_title)),
                __('View')
            );
        }

        // Önizleme linki
        if (in_array($post->post_status, array('pending', 'draft', 'future'))) {
            $preview_link = get_preview_post_link($post);
            if ($preview_link) {
                $new_actions['view'] = sprintf(
                    '<a href="%s" rel="bookmark" aria-label="%s">%s</a>',
                    esc_url($preview_link),
                    /* translators: %s: Post title. */
                    esc_attr(sprintf(__('Preview &#8220;%s&#8221;'), $post->post_title)),
                    __('Preview')
                );
            }
        }

        // Mevcut actions'ları yeni actions ile değiştir
        return $new_actions;
    }

    /**
     * Ürün düzenleme yetkilerini düzelt
     * Superole kullanıcıları için ürün düzenleme yetkilerini güçlendir
     */
    public function fix_product_edit_capabilities($caps, $cap, $user_id, $args) {
        // Sadece superole rolündeki kullanıcılar için
        $user = get_userdata($user_id);
        if (!$user || !in_array(self::SUPEROLE_ROLE, $user->roles)) {
            return $caps;
        }

        // Ürün ile ilgili tüm yetkileri bypass et
        $product_caps = [
            'edit_post', 'delete_post', 'read_post', 'publish_post',
            'edit_products', 'edit_others_products', 'edit_private_products', 'edit_published_products',
            'delete_products', 'delete_others_products', 'delete_private_products', 'delete_published_products',
            'read_products', 'read_private_products',
            'publish_products'
        ];

        if (in_array($cap, $product_caps)) {
            // Post ID'si varsa ve ürünse
            if (isset($args[0])) {
                $post_id = $args[0];
                $post = get_post($post_id);

                if ($post && $post->post_type === 'product') {
                    // Superole kullanıcıları tüm ürünleri düzenleyebilir
                    return array('exist');
                }
            } else {
                // Genel ürün yetkileri için
                return array('exist');
            }
        }

        return $caps;
    }

    /**
     * JavaScript ile ürün row actions'larını düzelt
     */
    public function add_product_row_actions_js() {
        // Sadece superole rolündeki kullanıcılar için
        if (!$this->is_current_user_superole()) {
            return;
        }

        // Sadece ürünler sayfasında
        global $typenow;
        if ($typenow !== 'product') {
            return;
        }

        ?>
        <script type="text/javascript">
        jQuery(document).ready(function($) {
            // Ürün satırlarını kontrol et ve eksik row actions'ları ekle
            $('.wp-list-table tbody tr').each(function() {
                var $row = $(this);
                var $rowActions = $row.find('.row-actions');
                var postId = $row.attr('id');

                if (postId && postId.indexOf('post-') === 0) {
                    var actualPostId = postId.replace('post-', '');

                    // Düzenle linki var mı kontrol et
                    if ($rowActions.find('.edit').length === 0) {
                        var editUrl = '<?php echo admin_url("post.php?action=edit&post="); ?>' + actualPostId;
                        var editLink = '<span class="edit"><a href="' + editUrl + '" aria-label="Düzenle">Düzenle</a> | </span>';
                        $rowActions.prepend(editLink);
                    }

                    // Hızlı düzenle linki var mı kontrol et
                    if ($rowActions.find('.inline').length === 0) {
                        var quickEditLink = '<span class="inline hide-if-no-js"><button type="button" class="button-link editinline" aria-label="Hızlı düzenle" aria-expanded="false">Hızlı Düzenle</button> | </span>';
                        $rowActions.find('.edit').after(quickEditLink);
                    }
                }
            });

            // Yeni ürün satırları eklendiğinde de çalıştır (AJAX sonrası)
            $(document).ajaxComplete(function() {
                setTimeout(function() {
                    $('.wp-list-table tbody tr').each(function() {
                        var $row = $(this);
                        var $rowActions = $row.find('.row-actions');
                        var postId = $row.attr('id');

                        if (postId && postId.indexOf('post-') === 0) {
                            var actualPostId = postId.replace('post-', '');

                            if ($rowActions.find('.edit').length === 0) {
                                var editUrl = '<?php echo admin_url("post.php?action=edit&post="); ?>' + actualPostId;
                                var editLink = '<span class="edit"><a href="' + editUrl + '" aria-label="Düzenle">Düzenle</a> | </span>';
                                $rowActions.prepend(editLink);
                            }

                            if ($rowActions.find('.inline').length === 0) {
                                var quickEditLink = '<span class="inline hide-if-no-js"><button type="button" class="button-link editinline" aria-label="Hızlı düzenle" aria-expanded="false">Hızlı Düzenle</button> | </span>';
                                $rowActions.find('.edit').after(quickEditLink);
                            }
                        }
                    });
                }, 100);
            });
        });
        </script>
        <?php
    }

    /**
     * Ürünler sayfasında view'ları filtrele
     */
    public function filter_product_views($views) {
        $current_user_id = get_current_user_id();

        // Kullanıcının toplam ürün sayısını al
        $user_products_count = count($this->get_user_product_ids($current_user_id));

        // Sadece kullanıcının ürün sayısını göster
        if (isset($views['all'])) {
            $views['all'] = str_replace(
                preg_replace('/\((\d+)\)/', '(' . $user_products_count . ')', $views['all']),
                $views['all'],
                $views['all']
            );
        }

        return $views;
    }

    /**
     * Kuponlar sayfasında view'ları filtrele
     */
    public function filter_coupon_views($views) {
        $current_user_id = get_current_user_id();

        // Kullanıcının toplam kupon sayısını al
        $user_coupons = get_posts([
            'post_type' => 'shop_coupon',
            'author' => $current_user_id,
            'posts_per_page' => -1,
            'fields' => 'ids',
            'post_status' => 'any'
        ]);

        $user_coupons_count = count($user_coupons);

        // Sadece kullanıcının kupon sayısını göster
        if (isset($views['all'])) {
            $views['all'] = str_replace(
                preg_replace('/\((\d+)\)/', '(' . $user_coupons_count . ')', $views['all']),
                $views['all'],
                $views['all']
            );
        }

        return $views;
    }

    /**
     * Siparişler sayfasında view'ları filtrele
     */
    public function filter_order_views($views) {
        // Bu fonksiyon siparişler için view sayılarını güncelleyebilir
        // Şimdilik basit bir implementasyon
        return $views;
    }

    /**
     * Kullanıcının ürün ID'lerini al (yardımcı fonksiyon)
     */
    private function get_user_product_ids($user_id) {
        return get_posts([
            'post_type' => 'product',
            'author' => $user_id,
            'posts_per_page' => -1,
            'fields' => 'ids',
            'post_status' => 'any'
        ]);
    }

    /**
     * Siparişler sayfasında olup olmadığını kontrol et
     */
    private function is_orders_page() {
        global $pagenow;

        // Klasik siparişler sayfası
        if ($pagenow === 'edit.php' && isset($_GET['post_type']) && $_GET['post_type'] === 'shop_order') {
            return true;
        }

        // WooCommerce admin siparişler sayfası
        if ($pagenow === 'admin.php' && isset($_GET['page']) && $_GET['page'] === 'wc-orders') {
            return true;
        }

        // HPOS siparişler sayfası
        if (isset($_GET['page']) && $_GET['page'] === 'wc-orders') {
            return true;
        }

        return false;
    }

    /**
     * Siparişler sayfası için özel handler
     */
    public function handle_orders_screen($current_screen) {
        // Sadece superole rolündeki kullanıcılar için
        if (!$this->is_current_user_superole()) {
            return;
        }

        // Siparişler sayfası kontrolü
        if (!isset($current_screen->id)) {
            return;
        }

        // WooCommerce siparişler sayfası
        if ($current_screen->id === 'woocommerce_page_wc-orders' ||
            $current_screen->id === 'edit-shop_order' ||
            strpos($current_screen->id, 'orders') !== false) {



            // Bu sayfada özel filtreleme uygula
            add_filter('woocommerce_order_list_table_prepare_items_query_args', [$this, 'filter_order_list_query_args'], 10, 1);
        }
    }

    /**
     * Sipariş listesi sorgu argümanlarını filtrele
     */
    public function filter_order_list_query_args($args) {
        // Sadece superole rolündeki kullanıcılar için
        if (!$this->is_current_user_superole()) {
            return $args;
        }

        $current_user_id = get_current_user_id();


        // Kullanıcının ürünlerini al
        $user_products = $this->get_user_product_ids($current_user_id);

        if (empty($user_products)) {
            // Kullanıcının hiç ürünü yoksa hiçbir sipariş döndürme
            $args['include'] = [0];
            return $args;
        }

        // Kullanıcının ürünlerini içeren siparişleri bul
        $order_ids = $this->get_orders_with_user_products($user_products);

        if (!empty($order_ids)) {
            $args['include'] = $order_ids;
        } else {
            $args['include'] = [0];
        }

        return $args;
    }

    /**
     * HPOS sipariş listesi için filtreleme
     */
    public function filter_hpos_order_list() {
        // Sadece superole rolündeki kullanıcılar için
        if (!$this->is_current_user_superole()) {
            return;
        }


    }

    /**
     * WooCommerce sipariş sorgu argümanlarını filtrele
     */
    public function filter_order_query_args($args) {
        // Sadece admin panelinde çalışsın
        if (!is_admin()) {
            return $args;
        }

        // Sadece superole rolündeki kullanıcılar için
        if (!$this->is_current_user_superole()) {
            return $args;
        }

        // Mevcut kullanıcı ID'sini al
        $current_user_id = get_current_user_id();



        // Kullanıcının ürünlerini al
        $user_products = $this->get_user_product_ids($current_user_id);

        if (empty($user_products)) {
            // Kullanıcının hiç ürünü yoksa hiçbir sipariş gösterme
            $args['post__in'] = [0]; // Hiçbir sipariş döndürme
            return $args;
        }

        // Kullanıcının ürünlerini içeren siparişleri bul
        $order_ids = $this->get_orders_with_user_products($user_products);

        if (!empty($order_ids)) {
            $args['post__in'] = $order_ids;
        } else {
            $args['post__in'] = [0]; // Hiçbir sipariş döndürme
        }

        return $args;
    }

    /**
     * Kullanıcının ürünlerini içeren siparişleri bul
     */
    private function get_orders_with_user_products($product_ids) {
        global $wpdb;

        if (empty($product_ids)) {
            return [];
        }

        $product_ids_str = implode(',', array_map('intval', $product_ids));

        $order_ids = $wpdb->get_col("
            SELECT DISTINCT order_items.order_id
            FROM {$wpdb->prefix}woocommerce_order_items as order_items
            INNER JOIN {$wpdb->prefix}woocommerce_order_itemmeta as order_item_meta
                ON order_items.order_item_id = order_item_meta.order_item_id
            WHERE order_item_meta.meta_key = '_product_id'
                AND order_item_meta.meta_value IN ({$product_ids_str})
        ");

        return array_map('intval', $order_ids);
    }

    /**
     * wc_get_orders argümanlarını filtrele
     */
    public function filter_wc_get_orders_args($args) {
        // Sadece admin panelinde çalışsın
        if (!is_admin()) {
            return $args;
        }

        // Sadece superole rolündeki kullanıcılar için
        if (!$this->is_current_user_superole()) {
            return $args;
        }

        $current_user_id = get_current_user_id();


        // Kullanıcının ürünlerini al
        $user_products = $this->get_user_product_ids($current_user_id);

        if (empty($user_products)) {
            // Kullanıcının hiç ürünü yoksa hiçbir sipariş döndürme
            $args['include'] = [0];
            return $args;
        }

        // Kullanıcının ürünlerini içeren siparişleri bul
        $order_ids = $this->get_orders_with_user_products($user_products);

        if (!empty($order_ids)) {
            $args['include'] = $order_ids;
        } else {
            $args['include'] = [0];
        }

        return $args;
    }

    /**
     * HPOS siparişlerini doğrudan filtrele
     */
    public function filter_hpos_orders_query($query, $query_vars) {
        // Sadece admin panelinde çalışsın
        if (!is_admin()) {
            return $query;
        }

        // Sadece superole rolündeki kullanıcılar için
        if (!$this->is_current_user_superole()) {
            return $query;
        }

        $current_user_id = get_current_user_id();
        error_log("Role Custom: HPOS orders query filtreleme - Kullanıcı ID: " . $current_user_id);

        // Kullanıcının ürünlerini al
        $user_products = $this->get_user_product_ids($current_user_id);

        if (empty($user_products)) {
            // Kullanıcının hiç ürünü yoksa hiçbir sipariş döndürme
            $query['post__in'] = [0];
            error_log("Role Custom: HPOS - Kullanıcının ürünü yok, siparişler gizleniyor");
            return $query;
        }

        // Kullanıcının ürünlerini içeren siparişleri bul
        $order_ids = $this->get_orders_with_user_products($user_products);

        if (!empty($order_ids)) {
            $query['post__in'] = $order_ids;
            error_log("Role Custom: HPOS - " . count($order_ids) . " sipariş bulundu: " . implode(', ', $order_ids));
        } else {
            $query['post__in'] = [0];
            error_log("Role Custom: HPOS - Kullanıcının ürünleriyle eşleşen sipariş bulunamadı");
        }

        return $query;
    }

    /**
     * HPOS tablo sorgusunu filtrele
     */
    public function filter_hpos_table_query($query) {
        // Sadece admin panelinde çalışsın
        if (!is_admin()) {
            return;
        }

        // Sadece superole rolündeki kullanıcılar için
        if (!$this->is_current_user_superole()) {
            return;
        }

        $current_user_id = get_current_user_id();
        error_log("Role Custom: HPOS table query filtreleme - Kullanıcı ID: " . $current_user_id);

        // Kullanıcının ürünlerini al
        $user_products = $this->get_user_product_ids($current_user_id);

        if (empty($user_products)) {
            // Kullanıcının hiç ürünü yoksa hiçbir sipariş döndürme
            $query->set('post__in', [0]);
            error_log("Role Custom: HPOS table - Kullanıcının ürünü yok, siparişler gizleniyor");
            return;
        }

        // Kullanıcının ürünlerini içeren siparişleri bul
        $order_ids = $this->get_orders_with_user_products($user_products);

        if (!empty($order_ids)) {
            $query->set('post__in', $order_ids);
            error_log("Role Custom: HPOS table - " . count($order_ids) . " sipariş bulundu: " . implode(', ', $order_ids));
        } else {
            $query->set('post__in', [0]);
            error_log("Role Custom: HPOS table - Kullanıcının ürünleriyle eşleşen sipariş bulunamadı");
        }
    }

    /**
     * JavaScript ile sipariş filtreleme ekle
     */
    public function add_order_filtering_js() {
        // Sadece superole rolündeki kullanıcılar için
        if (!$this->is_current_user_superole()) {
            return;
        }

        // Sadece siparişler sayfasında
        if (!$this->is_orders_page()) {
            return;
        }

        $current_user_id = get_current_user_id();
        $user_products = $this->get_user_product_ids($current_user_id);

        if (empty($user_products)) {
            // Kullanıcının ürünü yoksa tüm siparişleri gizle
            ?>
            <script type="text/javascript">
            jQuery(document).ready(function($) {
                // Tüm sipariş satırlarını gizle
                $('.wp-list-table tbody tr').hide();

                // Bilgi mesajı ekle
                if ($('.wp-list-table tbody tr').length > 0) {
                    $('.wp-list-table tbody').prepend(
                        '<tr><td colspan="10" style="text-align: center; padding: 20px; background: #f9f9f9;">' +
                        '<strong>Bu kullanıcının henüz ürünü bulunmadığı için sipariş görüntülenemiyor.</strong>' +
                        '</td></tr>'
                    );
                }
            });
            </script>
            <?php
            return;
        }

        $order_ids = $this->get_orders_with_user_products($user_products);
        $order_ids_json = json_encode($order_ids);

        ?>
        <script type="text/javascript">
        jQuery(document).ready(function($) {
            var allowedOrderIds = <?php echo $order_ids_json; ?>;

            // Sipariş satırlarını filtrele
            $('.wp-list-table tbody tr').each(function() {
                var $row = $(this);
                var rowId = $row.attr('id');

                if (rowId && rowId.indexOf('post-') === 0) {
                    var orderId = parseInt(rowId.replace('post-', ''));

                    if (allowedOrderIds.indexOf(orderId) === -1) {
                        $row.hide();
                    }
                }
            });

            // AJAX sonrası da çalıştır
            $(document).ajaxComplete(function() {
                setTimeout(function() {
                    $('.wp-list-table tbody tr').each(function() {
                        var $row = $(this);
                        var rowId = $row.attr('id');

                        if (rowId && rowId.indexOf('post-') === 0) {
                            var orderId = parseInt(rowId.replace('post-', ''));

                            if (allowedOrderIds.indexOf(orderId) === -1) {
                                $row.hide();
                            }
                        }
                    });
                }, 100);
            });
        });
        </script>
        <?php
    }

    /**
     * Kullanıcı düzenleme sayfasında rol değişikliği uyarısı ekle
     */
    public function add_role_change_warning_js() {
        global $user_id;

        if (!$user_id) {
            return;
        }

        $user = get_userdata($user_id);
        if (!$user) {
            return;
        }

        $has_superole = in_array(self::SUPEROLE_ROLE, $user->roles);

        ?>
        <script type="text/javascript">
        jQuery(document).ready(function($) {
            var hasSuperole = <?php echo $has_superole ? 'true' : 'false'; ?>;
            var originalRole = '<?php echo implode(',', $user->roles); ?>';

            // Rol değişikliği uyarısı
            $('#role, select[name="role"]').on('change', function() {
                var newRole = $(this).val();

                if (hasSuperole && newRole !== 'superole') {
                    if (confirm('Bu kullanıcı superole rolünden çıkarılıyor. Eğitmen yetkilerini kaybedecek. Devam etmek istiyor musunuz?')) {
                        // Devam et
                    } else {
                        // Geri al
                        $(this).val('superole');
                        return false;
                    }
                }

                if (!hasSuperole && newRole === 'superole') {
                    alert('Bu kullanıcı superole rolüne geçiriliyor. Eğitmen yetkilerine sahip olacak.');
                }
            });

            // Form submit edildiğinde cache temizleme uyarısı
            $('#your-profile').on('submit', function() {
                var currentRole = $('#role, select[name="role"]').val();

                if ((hasSuperole && currentRole !== 'superole') || (!hasSuperole && currentRole === 'superole')) {
                    // Rol değişikliği var, kullanıcıyı bilgilendir
                    $(this).append('<input type="hidden" name="role_changed" value="1">');
                }
            });
        });
        </script>
        <?php
    }

    /**
     * Reports menü sayfasını ekle
     */
    public function add_reports_menu() {
        // Sadece Superole rolündeki kullanıcılar için
        if (!$this->is_current_user_superole()) {
            return;
        }

        // WooCommerce aktif değilse çık
        if (!class_exists('WooCommerce')) {
            return;
        }

        // Reports ana menüsü
        add_menu_page(
            __('Raporlar', 'role-custom'),             // Sayfa başlığı
            __('Raporlar', 'role-custom'),             // Menü başlığı
            'view_woocommerce_reports',                // Yetki
            'role-custom-reports',                     // Menü slug'ı
            [$this, 'render_reports_page'],            // Callback fonksiyon
            'dashicons-chart-area',                    // İkon
            60                                         // Pozisyon
        );
    }

    /**
     * Reports sayfasını render et
     */
    public function render_reports_page() {
        // Güvenlik kontrolü
        if (!current_user_can('view_woocommerce_reports') || !$this->is_current_user_superole()) {
            wp_die(__('Bu sayfaya erişim yetkiniz yok.', 'role-custom'));
        }

        // WooCommerce aktif değilse uyarı göster
        if (!class_exists('WooCommerce')) {
            echo '<div class="notice notice-error"><p>' . __('WooCommerce eklentisi aktif değil.', 'role-custom') . '</p></div>';
            return;
        }

        // Kullanıcının ürün sayısını kontrol et
        $user_products = $this->get_user_products();
        if (empty($user_products)) {
            echo '<div class="wrap">';
            echo '<h1>' . __('Raporlar', 'role-custom') . '</h1>';
            echo '<div class="notice notice-info"><p>' . __('Henüz hiç ürününüz yok. Raporları görmek için önce ürün oluşturun.', 'role-custom') . '</p></div>';
            echo '</div>';
            return;
        }

        // Reports sayfası HTML'ini render et
        $this->render_reports_html();
    }

    /**
     * Kullanıcının ürünlerini al
     */
    private function get_user_products() {
        $current_user_id = get_current_user_id();

        // Güvenlik kontrolü - sadece superole kullanıcıları
        if (!$this->is_current_user_superole()) {
            return [];
        }

        $args = [
            'post_type' => 'product',
            'post_status' => 'any',
            'author' => $current_user_id,
            'posts_per_page' => -1,
            'fields' => 'ids'
        ];

        $products = get_posts($args);

        // Ek güvenlik: Ürünlerin gerçekten bu kullanıcıya ait olduğunu doğrula
        $verified_products = [];
        foreach ($products as $product_id) {
            $product_author = get_post_field('post_author', $product_id);
            if ($product_author == $current_user_id) {
                $verified_products[] = $product_id;
            }
        }

        return $verified_products;
    }

    /**
     * Reports sayfası HTML'ini render et
     */
    private function render_reports_html() {
        ?>
        <div class="wrap role-custom-reports">
            <h1><?php _e('Raporlar', 'role-custom'); ?></h1>

            <!-- Özet Kartları -->
            <div class="role-custom-summary-cards">
                <div class="summary-card">
                    <div class="card-icon">
                        <span class="dashicons dashicons-cart"></span>
                    </div>
                    <div class="card-content">
                        <h3 id="total-orders">-</h3>
                        <p><?php _e('Toplam Sipariş', 'role-custom'); ?></p>
                    </div>
                </div>

                <div class="summary-card">
                    <div class="card-icon">
                        <span class="dashicons dashicons-money-alt"></span>
                    </div>
                    <div class="card-content">
                        <h3 id="total-revenue">-</h3>
                        <p><?php _e('Toplam Gelir', 'role-custom'); ?></p>
                    </div>
                </div>

                <div class="summary-card">
                    <div class="card-icon">
                        <span class="dashicons dashicons-products"></span>
                    </div>
                    <div class="card-content">
                        <h3 id="total-products">-</h3>
                        <p><?php _e('Toplam Ürün', 'role-custom'); ?></p>
                    </div>
                </div>

                <div class="summary-card">
                    <div class="card-icon">
                        <span class="dashicons dashicons-star-filled"></span>
                    </div>
                    <div class="card-content">
                        <h3 id="avg-rating">-</h3>
                        <p><?php _e('Ortalama Puan', 'role-custom'); ?></p>
                    </div>
                </div>
            </div>

            <!-- Grafik Alanları -->
            <div class="role-custom-charts-container">
                <!-- Satış Grafiği -->
                <div class="chart-section">
                    <div class="chart-header">
                        <h2><?php _e('Satış Trendi', 'role-custom'); ?></h2>
                        <div class="chart-controls">
                            <select id="sales-period">
                                <option value="7"><?php _e('Son 7 Gün', 'role-custom'); ?></option>
                                <option value="30" selected><?php _e('Son 30 Gün', 'role-custom'); ?></option>
                                <option value="90"><?php _e('Son 90 Gün', 'role-custom'); ?></option>
                                <option value="custom"><?php _e('Özel Tarih Aralığı', 'role-custom'); ?></option>
                            </select>
                            <div id="sales-custom-dates" class="custom-date-range" style="display: none;">
                                <input type="date" id="sales-start-date" />
                                <span><?php _e('ile', 'role-custom'); ?></span>
                                <input type="date" id="sales-end-date" />
                                <button type="button" id="sales-apply-dates" class="button button-primary"><?php _e('Uygula', 'role-custom'); ?></button>
                            </div>
                        </div>
                    </div>
                    <div class="chart-container">
                        <canvas id="sales-chart"></canvas>
                    </div>
                </div>

                <!-- Gelir Grafiği -->
                <div class="chart-section">
                    <div class="chart-header">
                        <h2><?php _e('Gelir Trendi', 'role-custom'); ?></h2>
                        <div class="chart-controls">
                            <select id="revenue-period">
                                <option value="7"><?php _e('Son 7 Gün', 'role-custom'); ?></option>
                                <option value="30" selected><?php _e('Son 30 Gün', 'role-custom'); ?></option>
                                <option value="90"><?php _e('Son 90 Gün', 'role-custom'); ?></option>
                                <option value="custom"><?php _e('Özel Tarih Aralığı', 'role-custom'); ?></option>
                            </select>
                            <div id="revenue-custom-dates" class="custom-date-range" style="display: none;">
                                <input type="date" id="revenue-start-date" />
                                <span><?php _e('ile', 'role-custom'); ?></span>
                                <input type="date" id="revenue-end-date" />
                                <button type="button" id="revenue-apply-dates" class="button button-primary"><?php _e('Uygula', 'role-custom'); ?></button>
                            </div>
                        </div>
                    </div>
                    <div class="chart-container">
                        <canvas id="revenue-chart"></canvas>
                    </div>
                </div>

                <!-- En Çok Satan Ürünler -->
                <div class="chart-section">
                    <div class="chart-header">
                        <h2><?php _e('En Çok Satan Ürünler', 'role-custom'); ?></h2>
                    </div>
                    <div class="chart-container">
                        <canvas id="products-chart"></canvas>
                    </div>
                </div>
            </div>

            <!-- Loading Overlay -->
            <div id="reports-loading" class="reports-loading" style="display: none;">
                <div class="loading-spinner"></div>
                <p><?php _e('Veriler yükleniyor...', 'role-custom'); ?></p>
            </div>
        </div>
        <?php
    }

    /**
     * Kullanıcının sipariş verilerini al
     */
    private function get_user_orders_data($days = 30) {
        $current_user_id = get_current_user_id();
        $user_products = $this->get_user_products();

        if (empty($user_products)) {
            return [];
        }

        // Tarih aralığını hesapla
        $end_date = current_time('Y-m-d');
        $start_date = date('Y-m-d', strtotime("-{$days} days"));

        global $wpdb;

        // HPOS (High Performance Order Storage) kontrolü
        if (class_exists('Automattic\WooCommerce\Utilities\OrderUtil') &&
            \Automattic\WooCommerce\Utilities\OrderUtil::custom_orders_table_usage_is_enabled()) {

            // HPOS kullanılıyor
            $orders_table = $wpdb->prefix . 'wc_orders';
            $order_items_table = $wpdb->prefix . 'woocommerce_order_items';
            $order_itemmeta_table = $wpdb->prefix . 'woocommerce_order_itemmeta';

            $query = $wpdb->prepare("
                SELECT
                    DATE(o.date_created_gmt) as order_date,
                    COUNT(DISTINCT o.id) as order_count,
                    SUM(CAST(oim_total.meta_value AS DECIMAL(10,2))) as total_revenue,
                    SUM(CAST(oim_qty.meta_value AS SIGNED)) as total_quantity
                FROM {$orders_table} o
                INNER JOIN {$order_items_table} oi ON o.id = oi.order_id
                INNER JOIN {$order_itemmeta_table} oim_product ON oi.order_item_id = oim_product.order_item_id
                    AND oim_product.meta_key = '_product_id'
                LEFT JOIN {$order_itemmeta_table} oim_total ON oi.order_item_id = oim_total.order_item_id
                    AND oim_total.meta_key = '_line_total'
                LEFT JOIN {$order_itemmeta_table} oim_qty ON oi.order_item_id = oim_qty.order_item_id
                    AND oim_qty.meta_key = '_qty'
                WHERE o.status IN ('wc-completed', 'wc-processing', 'wc-on-hold')
                    AND DATE(o.date_created_gmt) BETWEEN %s AND %s
                    AND CAST(oim_product.meta_value AS UNSIGNED) IN (" . implode(',', array_map('intval', $user_products)) . ")
                GROUP BY DATE(o.date_created_gmt)
                ORDER BY order_date ASC
            ", $start_date, $end_date);

        } else {
            // Geleneksel post tablosu kullanılıyor
            $query = $wpdb->prepare("
                SELECT
                    DATE(p.post_date) as order_date,
                    COUNT(DISTINCT p.ID) as order_count,
                    SUM(CAST(oim_total.meta_value AS DECIMAL(10,2))) as total_revenue,
                    SUM(CAST(oim_qty.meta_value AS SIGNED)) as total_quantity
                FROM {$wpdb->posts} p
                INNER JOIN {$wpdb->prefix}woocommerce_order_items oi ON p.ID = oi.order_id
                INNER JOIN {$wpdb->prefix}woocommerce_order_itemmeta oim_product ON oi.order_item_id = oim_product.order_item_id
                    AND oim_product.meta_key = '_product_id'
                LEFT JOIN {$wpdb->prefix}woocommerce_order_itemmeta oim_total ON oi.order_item_id = oim_total.order_item_id
                    AND oim_total.meta_key = '_line_total'
                LEFT JOIN {$wpdb->prefix}woocommerce_order_itemmeta oim_qty ON oi.order_item_id = oim_qty.order_item_id
                    AND oim_qty.meta_key = '_qty'
                WHERE p.post_type = 'shop_order'
                    AND p.post_status IN ('wc-completed', 'wc-processing', 'wc-on-hold')
                    AND DATE(p.post_date) BETWEEN %s AND %s
                    AND CAST(oim_product.meta_value AS UNSIGNED) IN (" . implode(',', array_map('intval', $user_products)) . ")
                GROUP BY DATE(p.post_date)
                ORDER BY order_date ASC
            ", $start_date, $end_date);
        }

        $results = $wpdb->get_results($query);

        // Eksik günleri sıfır değerlerle doldur
        $data = [];
        $current_date = $start_date;

        while ($current_date <= $end_date) {
            $found = false;
            foreach ($results as $result) {
                if ($result->order_date === $current_date) {
                    $data[] = [
                        'date' => $current_date,
                        'orders' => (int) $result->order_count,
                        'revenue' => (float) $result->total_revenue,
                        'quantity' => (int) $result->total_quantity
                    ];
                    $found = true;
                    break;
                }
            }

            if (!$found) {
                $data[] = [
                    'date' => $current_date,
                    'orders' => 0,
                    'revenue' => 0,
                    'quantity' => 0
                ];
            }

            $current_date = date('Y-m-d', strtotime($current_date . ' +1 day'));
        }

        return $data;
    }

    /**
     * Kullanıcının sipariş verilerini tarih aralığına göre al
     */
    private function get_user_orders_data_by_range($start_date, $end_date) {
        $current_user_id = get_current_user_id();
        $user_products = $this->get_user_products();

        if (empty($user_products)) {
            return [];
        }

        // Tarih formatını doğrula
        $start_date = date('Y-m-d', strtotime($start_date));
        $end_date = date('Y-m-d', strtotime($end_date));

        global $wpdb;

        // HPOS (High Performance Order Storage) kontrolü
        if (class_exists('Automattic\WooCommerce\Utilities\OrderUtil') &&
            \Automattic\WooCommerce\Utilities\OrderUtil::custom_orders_table_usage_is_enabled()) {

            // HPOS kullanılıyor
            $orders_table = $wpdb->prefix . 'wc_orders';
            $order_items_table = $wpdb->prefix . 'woocommerce_order_items';
            $order_itemmeta_table = $wpdb->prefix . 'woocommerce_order_itemmeta';

            $query = $wpdb->prepare("
                SELECT
                    DATE(o.date_created_gmt) as order_date,
                    COUNT(DISTINCT o.id) as order_count,
                    SUM(CAST(oim_total.meta_value AS DECIMAL(10,2))) as total_revenue,
                    SUM(CAST(oim_qty.meta_value AS SIGNED)) as total_quantity
                FROM {$orders_table} o
                INNER JOIN {$order_items_table} oi ON o.id = oi.order_id
                INNER JOIN {$order_itemmeta_table} oim_product ON oi.order_item_id = oim_product.order_item_id
                    AND oim_product.meta_key = '_product_id'
                LEFT JOIN {$order_itemmeta_table} oim_total ON oi.order_item_id = oim_total.order_item_id
                    AND oim_total.meta_key = '_line_total'
                LEFT JOIN {$order_itemmeta_table} oim_qty ON oi.order_item_id = oim_qty.order_item_id
                    AND oim_qty.meta_key = '_qty'
                WHERE o.status IN ('wc-completed', 'wc-processing', 'wc-on-hold')
                    AND DATE(o.date_created_gmt) BETWEEN %s AND %s
                    AND CAST(oim_product.meta_value AS UNSIGNED) IN (" . implode(',', array_map('intval', $user_products)) . ")
                GROUP BY DATE(o.date_created_gmt)
                ORDER BY order_date ASC
            ", $start_date, $end_date);

        } else {
            // Geleneksel post tablosu kullanılıyor
            $query = $wpdb->prepare("
                SELECT
                    DATE(p.post_date) as order_date,
                    COUNT(DISTINCT p.ID) as order_count,
                    SUM(CAST(oim_total.meta_value AS DECIMAL(10,2))) as total_revenue,
                    SUM(CAST(oim_qty.meta_value AS SIGNED)) as total_quantity
                FROM {$wpdb->posts} p
                INNER JOIN {$wpdb->prefix}woocommerce_order_items oi ON p.ID = oi.order_id
                INNER JOIN {$wpdb->prefix}woocommerce_order_itemmeta oim_product ON oi.order_item_id = oim_product.order_item_id
                    AND oim_product.meta_key = '_product_id'
                LEFT JOIN {$wpdb->prefix}woocommerce_order_itemmeta oim_total ON oi.order_item_id = oim_total.order_item_id
                    AND oim_total.meta_key = '_line_total'
                LEFT JOIN {$wpdb->prefix}woocommerce_order_itemmeta oim_qty ON oi.order_item_id = oim_qty.order_item_id
                    AND oim_qty.meta_key = '_qty'
                WHERE p.post_type = 'shop_order'
                    AND p.post_status IN ('wc-completed', 'wc-processing', 'wc-on-hold')
                    AND DATE(p.post_date) BETWEEN %s AND %s
                    AND CAST(oim_product.meta_value AS UNSIGNED) IN (" . implode(',', array_map('intval', $user_products)) . ")
                GROUP BY DATE(p.post_date)
                ORDER BY order_date ASC
            ", $start_date, $end_date);
        }

        $results = $wpdb->get_results($query);

        // Eksik günleri sıfır değerlerle doldur
        $data = [];
        $current_date = $start_date;

        while ($current_date <= $end_date) {
            $found = false;
            foreach ($results as $result) {
                if ($result->order_date === $current_date) {
                    $data[] = [
                        'date' => $current_date,
                        'orders' => (int) $result->order_count,
                        'revenue' => (float) $result->total_revenue,
                        'quantity' => (int) $result->total_quantity
                    ];
                    $found = true;
                    break;
                }
            }

            if (!$found) {
                $data[] = [
                    'date' => $current_date,
                    'orders' => 0,
                    'revenue' => 0,
                    'quantity' => 0
                ];
            }

            $current_date = date('Y-m-d', strtotime($current_date . ' +1 day'));
        }

        return $data;
    }

    /**
     * Kullanıcının en çok satan ürünlerini al
     */
    private function get_top_selling_products($limit = 10) {
        $current_user_id = get_current_user_id();
        $user_products = $this->get_user_products();

        if (empty($user_products)) {
            return [];
        }

        global $wpdb;

        // HPOS kontrolü
        if (class_exists('Automattic\WooCommerce\Utilities\OrderUtil') &&
            \Automattic\WooCommerce\Utilities\OrderUtil::custom_orders_table_usage_is_enabled()) {

            $orders_table = $wpdb->prefix . 'wc_orders';
            $order_items_table = $wpdb->prefix . 'woocommerce_order_items';
            $order_itemmeta_table = $wpdb->prefix . 'woocommerce_order_itemmeta';

            $query = $wpdb->prepare("
                SELECT
                    CAST(oim_product.meta_value AS UNSIGNED) as product_id,
                    p.post_title as product_name,
                    SUM(CAST(oim_qty.meta_value AS SIGNED)) as total_sold,
                    SUM(CAST(oim_total.meta_value AS DECIMAL(10,2))) as total_revenue
                FROM {$orders_table} o
                INNER JOIN {$order_items_table} oi ON o.id = oi.order_id
                INNER JOIN {$order_itemmeta_table} oim_product ON oi.order_item_id = oim_product.order_item_id
                    AND oim_product.meta_key = '_product_id'
                INNER JOIN {$wpdb->posts} p ON CAST(oim_product.meta_value AS UNSIGNED) = p.ID
                LEFT JOIN {$order_itemmeta_table} oim_total ON oi.order_item_id = oim_total.order_item_id
                    AND oim_total.meta_key = '_line_total'
                LEFT JOIN {$order_itemmeta_table} oim_qty ON oi.order_item_id = oim_qty.order_item_id
                    AND oim_qty.meta_key = '_qty'
                WHERE o.status IN ('wc-completed', 'wc-processing', 'wc-on-hold')
                    AND CAST(oim_product.meta_value AS UNSIGNED) IN (" . implode(',', array_map('intval', $user_products)) . ")
                GROUP BY product_id, product_name
                ORDER BY total_sold DESC
                LIMIT %d
            ", $limit);

        } else {
            $query = $wpdb->prepare("
                SELECT
                    CAST(oim_product.meta_value AS UNSIGNED) as product_id,
                    p.post_title as product_name,
                    SUM(CAST(oim_qty.meta_value AS SIGNED)) as total_sold,
                    SUM(CAST(oim_total.meta_value AS DECIMAL(10,2))) as total_revenue
                FROM {$wpdb->posts} po
                INNER JOIN {$wpdb->prefix}woocommerce_order_items oi ON po.ID = oi.order_id
                INNER JOIN {$wpdb->prefix}woocommerce_order_itemmeta oim_product ON oi.order_item_id = oim_product.order_item_id
                    AND oim_product.meta_key = '_product_id'
                INNER JOIN {$wpdb->posts} p ON CAST(oim_product.meta_value AS UNSIGNED) = p.ID
                LEFT JOIN {$wpdb->prefix}woocommerce_order_itemmeta oim_total ON oi.order_item_id = oim_total.order_item_id
                    AND oim_total.meta_key = '_line_total'
                LEFT JOIN {$wpdb->prefix}woocommerce_order_itemmeta oim_qty ON oi.order_item_id = oim_qty.order_item_id
                    AND oim_qty.meta_key = '_qty'
                WHERE po.post_type = 'shop_order'
                    AND po.post_status IN ('wc-completed', 'wc-processing', 'wc-on-hold')
                    AND CAST(oim_product.meta_value AS UNSIGNED) IN (" . implode(',', array_map('intval', $user_products)) . ")
                GROUP BY product_id, product_name
                ORDER BY total_sold DESC
                LIMIT %d
            ", $limit);
        }

        return $wpdb->get_results($query);
    }

    /**
     * Kullanıcının genel istatistiklerini al
     */
    private function get_user_stats() {
        $current_user_id = get_current_user_id();
        $user_products = $this->get_user_products();

        $stats = [
            'total_products' => count($user_products),
            'total_orders' => 0,
            'total_revenue' => 0,
            'avg_rating' => 0
        ];

        if (empty($user_products)) {
            return $stats;
        }

        global $wpdb;

        // Toplam sipariş ve gelir hesapla
        if (class_exists('Automattic\WooCommerce\Utilities\OrderUtil') &&
            \Automattic\WooCommerce\Utilities\OrderUtil::custom_orders_table_usage_is_enabled()) {

            $orders_table = $wpdb->prefix . 'wc_orders';
            $order_items_table = $wpdb->prefix . 'woocommerce_order_items';
            $order_itemmeta_table = $wpdb->prefix . 'woocommerce_order_itemmeta';

            $query = "
                SELECT
                    COUNT(DISTINCT o.id) as total_orders,
                    SUM(CAST(oim_total.meta_value AS DECIMAL(10,2))) as total_revenue
                FROM {$orders_table} o
                INNER JOIN {$order_items_table} oi ON o.id = oi.order_id
                INNER JOIN {$order_itemmeta_table} oim_product ON oi.order_item_id = oim_product.order_item_id
                    AND oim_product.meta_key = '_product_id'
                LEFT JOIN {$order_itemmeta_table} oim_total ON oi.order_item_id = oim_total.order_item_id
                    AND oim_total.meta_key = '_line_total'
                WHERE o.status IN ('wc-completed', 'wc-processing', 'wc-on-hold')
                    AND CAST(oim_product.meta_value AS UNSIGNED) IN (" . implode(',', array_map('intval', $user_products)) . ")
            ";

        } else {
            $query = "
                SELECT
                    COUNT(DISTINCT p.ID) as total_orders,
                    SUM(CAST(oim_total.meta_value AS DECIMAL(10,2))) as total_revenue
                FROM {$wpdb->posts} p
                INNER JOIN {$wpdb->prefix}woocommerce_order_items oi ON p.ID = oi.order_id
                INNER JOIN {$wpdb->prefix}woocommerce_order_itemmeta oim_product ON oi.order_item_id = oim_product.order_item_id
                    AND oim_product.meta_key = '_product_id'
                LEFT JOIN {$wpdb->prefix}woocommerce_order_itemmeta oim_total ON oi.order_item_id = oim_total.order_item_id
                    AND oim_total.meta_key = '_line_total'
                WHERE p.post_type = 'shop_order'
                    AND p.post_status IN ('wc-completed', 'wc-processing', 'wc-on-hold')
                    AND CAST(oim_product.meta_value AS UNSIGNED) IN (" . implode(',', array_map('intval', $user_products)) . ")
            ";
        }

        $result = $wpdb->get_row($query);

        if ($result) {
            $stats['total_orders'] = (int) $result->total_orders;
            $stats['total_revenue'] = (float) $result->total_revenue;
        }

        // Ortalama puan hesapla
        $rating_query = "
            SELECT AVG(CAST(cm.meta_value AS DECIMAL(3,2))) as avg_rating
            FROM {$wpdb->comments} c
            INNER JOIN {$wpdb->commentmeta} cm ON c.comment_ID = cm.comment_id
            WHERE c.comment_type = 'review'
                AND c.comment_approved = '1'
                AND cm.meta_key = 'rating'
                AND c.comment_post_ID IN (" . implode(',', array_map('intval', $user_products)) . ")
        ";

        $rating_result = $wpdb->get_var($rating_query);
        $stats['avg_rating'] = $rating_result ? round((float) $rating_result, 1) : 0;

        return $stats;
    }

    /**
     * AJAX: Satış verilerini al
     */
    public function ajax_get_sales_data() {
        // Güvenlik kontrolü
        if (!current_user_can('view_woocommerce_reports') || !$this->is_current_user_superole()) {
            wp_send_json_error(['message' => 'Unauthorized access']);
            return;
        }

        // Nonce kontrolü
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'role_custom_reports_nonce')) {
            wp_send_json_error(['message' => 'Invalid security token']);
            return;
        }

        // WooCommerce aktif kontrolü
        if (!class_exists('WooCommerce')) {
            wp_send_json_error(['message' => 'WooCommerce not active']);
            return;
        }

        // Tarih parametrelerini al
        $start_date = isset($_POST['start_date']) ? sanitize_text_field($_POST['start_date']) : '';
        $end_date = isset($_POST['end_date']) ? sanitize_text_field($_POST['end_date']) : '';
        $days = isset($_POST['days']) ? intval($_POST['days']) : 30;

        // Özel tarih aralığı kontrolü
        if (!empty($start_date) && !empty($end_date)) {
            $data = $this->get_user_orders_data_by_range($start_date, $end_date);
        } else {
            // Gün sayısını doğrula (1-365 arası)
            $days = max(1, min(365, $days));
            $data = $this->get_user_orders_data($days);
        }

        wp_send_json_success($data);
    }

    /**
     * AJAX: Gelir verilerini al
     */
    public function ajax_get_revenue_data() {
        // Güvenlik kontrolü
        if (!current_user_can('view_woocommerce_reports') || !$this->is_current_user_superole()) {
            wp_send_json_error(['message' => 'Unauthorized access']);
            return;
        }

        // Nonce kontrolü
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'role_custom_reports_nonce')) {
            wp_send_json_error(['message' => 'Invalid security token']);
            return;
        }

        // WooCommerce aktif kontrolü
        if (!class_exists('WooCommerce')) {
            wp_send_json_error(['message' => 'WooCommerce not active']);
            return;
        }

        // Tarih parametrelerini al
        $start_date = isset($_POST['start_date']) ? sanitize_text_field($_POST['start_date']) : '';
        $end_date = isset($_POST['end_date']) ? sanitize_text_field($_POST['end_date']) : '';
        $days = isset($_POST['days']) ? intval($_POST['days']) : 30;

        // Özel tarih aralığı kontrolü
        if (!empty($start_date) && !empty($end_date)) {
            $data = $this->get_user_orders_data_by_range($start_date, $end_date);
        } else {
            // Gün sayısını doğrula (1-365 arası)
            $days = max(1, min(365, $days));
            $data = $this->get_user_orders_data($days);
        }

        wp_send_json_success($data);
    }

    /**
     * AJAX: Ürün performans verilerini al
     */
    public function ajax_get_product_performance() {
        // Güvenlik kontrolü
        if (!current_user_can('view_woocommerce_reports') || !$this->is_current_user_superole()) {
            wp_send_json_error(['message' => 'Unauthorized access']);
            return;
        }

        // Nonce kontrolü
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'role_custom_reports_nonce')) {
            wp_send_json_error(['message' => 'Invalid security token']);
            return;
        }

        // WooCommerce aktif kontrolü
        if (!class_exists('WooCommerce')) {
            wp_send_json_error(['message' => 'WooCommerce not active']);
            return;
        }

        // Limit değerini doğrula (1-50 arası)
        $limit = isset($_POST['limit']) ? intval($_POST['limit']) : 10;
        $limit = max(1, min(50, $limit));

        $data = $this->get_top_selling_products($limit);

        wp_send_json_success($data);
    }

    /**
     * Reports sayfası için script ve style dosyalarını yükle
     */
    public function enqueue_reports_scripts($hook) {
        // Sadece reports sayfasında yükle
        if ($hook !== 'toplevel_page_role-custom-reports') {
            return;
        }

        // Sadece Superole rolündeki kullanıcılar için
        if (!$this->is_current_user_superole()) {
            return;
        }

        // Chart.js kütüphanesini yükle
        wp_enqueue_script(
            'chartjs',
            'https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js',
            [],
            '3.9.1',
            true
        );

        // Reports JavaScript dosyasını yükle
        wp_enqueue_script(
            'role-custom-reports',
            ROLE_CUSTOM_PLUGIN_URL . 'assets/js/reports.js',
            ['jquery', 'chartjs'],
            ROLE_CUSTOM_VERSION,
            true
        );

        // Reports CSS dosyasını yükle
        wp_enqueue_style(
            'role-custom-reports',
            ROLE_CUSTOM_PLUGIN_URL . 'assets/css/reports.css',
            [],
            ROLE_CUSTOM_VERSION
        );

        // JavaScript'e veri gönder
        wp_localize_script('role-custom-reports', 'roleCustomReports', [
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('role_custom_reports_nonce'),
            'strings' => [
                'loading' => __('Yükleniyor...', 'role-custom'),
                'error' => __('Veri yüklenirken hata oluştu.', 'role-custom'),
                'noData' => __('Gösterilecek veri yok.', 'role-custom'),
                'orders' => __('Siparişler', 'role-custom'),
                'revenue' => __('Gelir', 'role-custom'),
                'quantity' => __('Adet', 'role-custom'),
                'currency' => get_woocommerce_currency_symbol()
            ]
        ]);

        // Sayfa yüklendiğinde genel istatistikleri al
        $stats = $this->get_user_stats();
        wp_localize_script('role-custom-reports', 'roleCustomStats', $stats);
    }
}

// Eklentiyi başlat
function role_custom_init() {
    return Role_Custom::get_instance();
}

// WordPress yüklendikten sonra eklentiyi başlat
add_action('plugins_loaded', 'role_custom_init');

// Eklenti bilgilerini döndüren yardımcı fonksiyon
function role_custom() {
    return Role_Custom::get_instance();
}

// Eklenti etkinleştirme/devre dışı bırakma hook'ları
register_activation_hook(ROLE_CUSTOM_PLUGIN_FILE, 'role_custom_activate');
register_deactivation_hook(ROLE_CUSTOM_PLUGIN_FILE, 'role_custom_deactivate');

/**
 * Eklenti etkinleştirme fonksiyonu
 */
function role_custom_activate() {
    $role_custom = Role_Custom::get_instance();
    $role_custom->activate();
}

/**
 * Eklenti devre dışı bırakma fonksiyonu
 */
function role_custom_deactivate() {
    $role_custom = Role_Custom::get_instance();
    $role_custom->deactivate();
}

/**
 * Global fonksiyon: Eğitmen meta verilerini temizle
 * 36 eklentisindeki clean_instructor_meta_data() fonksiyonunun aynısı
 *
 * @param int $user_id Kullanıcı ID'si
 * @return bool İşlem başarılı oldu mu
 */
function role_custom_clean_instructor_meta_data($user_id) {
    $role_custom = Role_Custom::get_instance();
    return $role_custom->clean_instructor_meta_data($user_id);
}

/**
 * Kullanıcı rolü değiştiğinde eğitmen meta verilerini temizleyen hook fonksiyonu
 * 36 eklentisindeki check_admin_bar_on_role_change() fonksiyonunun mantığı
 *
 * @param int $user_id Kullanıcı ID'si
 * @param string $role Yeni rol
 * @param array $old_roles Eski roller
 */
function role_custom_check_role_change_for_instructor_cleanup($user_id, $role, $old_roles) {
    // Admin rolü ise işlem yapma
    if ($role === 'administrator') {
        return;
    }

    // Eğer kullanıcı tutor_instructor rolünden subscriber rolüne geçiyorsa
    if ($role === 'subscriber' && in_array('tutor_instructor', $old_roles)) {
        role_custom_clean_instructor_meta_data($user_id);
        error_log("Role Custom: Kullanıcı (ID: {$user_id}) tutor_instructor'dan subscriber'a geçti, meta veriler temizlendi.");
    }

    // Eğer kullanıcı superole rolünden başka bir role geçiyorsa
    if (in_array('superole', $old_roles) && $role !== 'superole') {
        $role_custom = Role_Custom::get_instance();
        $role_custom->cleanup_superole_instructor_meta($user_id);
        error_log("Role Custom: Kullanıcı (ID: {$user_id}) superole'den {$role}'e geçti, meta veriler temizlendi.");
    }
}

// Hook'u ekle - 36 eklentisindeki gibi
add_action('set_user_role', 'role_custom_check_role_change_for_instructor_cleanup', 10, 3);

// Debug admin sayfasını dahil et (sadece admin panelinde)
if (is_admin()) {
    require_once ROLE_CUSTOM_PLUGIN_DIR . 'admin-debug.php';
}

// Test dosyasını dahil et (sadece debug modunda)
if (defined('WP_DEBUG') && WP_DEBUG && is_admin()) {
    if (file_exists(ROLE_CUSTOM_PLUGIN_DIR . 'tests/test-role-custom.php')) {
        require_once ROLE_CUSTOM_PLUGIN_DIR . 'tests/test-role-custom.php';
    }

    // Reports test dosyasını dahil et
    if (file_exists(ROLE_CUSTOM_PLUGIN_DIR . 'test-reports.php')) {
        require_once ROLE_CUSTOM_PLUGIN_DIR . 'test-reports.php';
    }

    // Reports fixes test dosyasını dahil et
    if (file_exists(ROLE_CUSTOM_PLUGIN_DIR . 'test-reports-fixes.php')) {
        require_once ROLE_CUSTOM_PLUGIN_DIR . 'test-reports-fixes.php';
    }
}
